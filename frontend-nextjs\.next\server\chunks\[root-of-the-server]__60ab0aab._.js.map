{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/validation/billing-schemas.ts"], "sourcesContent": ["// Comprehensive validation schemas for billing forms\n// Provides robust client-side validation with detailed error messages in Chinese\n\nimport * as z from 'zod';\n\n// Common validation patterns\nconst positiveNumber = z.number().min(0, '金额不能为负数');\nconst requiredString = z.string().min(1, '此字段为必填项');\nconst optionalString = z.string();\nconst phoneRegex = /^1[3-9]\\d{9}$/;\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\n// Custom validation functions\nconst validateCurrency = (value: number) => {\n  if (value < 0) return false;\n  // Check for reasonable decimal places (max 2)\n  const decimalPlaces = (value.toString().split('.')[1] || '').length;\n  return decimalPlaces <= 2;\n};\n\nconst validateDateNotInPast = (date: string) => {\n  const inputDate = new Date(date);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return inputDate >= today;\n};\n\nconst validateDateNotTooFarInFuture = (date: string) => {\n  const inputDate = new Date(date);\n  const maxDate = new Date();\n  maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future\n  return inputDate <= maxDate;\n};\n\n// Bill Item validation schema\nexport const billItemSchema = z.object({\n  itemType: z.enum(['treatment', 'consultation', 'material', 'service'], {\n    required_error: '请选择项目类型',\n    invalid_type_error: '无效的项目类型',\n  }),\n  itemName: requiredString.max(100, '项目名称不能超过100个字符'),\n  description: optionalString.max(500, '描述不能超过500个字符').optional(),\n  quantity: z.number()\n    .min(0.01, '数量必须大于0')\n    .max(9999, '数量不能超过9999')\n    .refine((val) => {\n      const decimalPlaces = (val.toString().split('.')[1] || '').length;\n      return decimalPlaces <= 3;\n    }, '数量最多支持3位小数'),\n  unitPrice: z.number()\n    .min(0, '单价不能为负数')\n    .max(999999.99, '单价不能超过999,999.99')\n    .refine(validateCurrency, '单价格式无效，最多支持2位小数'),\n  discountRate: z.number()\n    .min(0, '折扣率不能为负数')\n    .max(100, '折扣率不能超过100%')\n    .optional(),\n}).refine((data) => {\n  // Validate that discount rate makes sense\n  if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {\n    return false;\n  }\n  return true;\n}, {\n  message: '单价为0时不能设置折扣',\n  path: ['discountRate'],\n});\n\n// Bill form validation schema\nexport const billFormSchema = z.object({\n  patient: requiredString.uuid('请选择有效的患者'),\n  appointment: optionalString.uuid('请选择有效的预约').optional().or(z.literal('')),\n  treatment: optionalString.uuid('请选择有效的治疗项目').optional().or(z.literal('')),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional'], {\n    required_error: '请选择账单类型',\n    invalid_type_error: '无效的账单类型',\n  }),\n  description: requiredString\n    .min(2, '账单描述至少需要2个字符')\n    .max(200, '账单描述不能超过200个字符'),\n  notes: optionalString.max(1000, '备注不能超过1000个字符').optional(),\n  dueDate: z.string()\n    .min(1, '请选择到期日期')\n    .refine((date) => {\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的日期')\n    .refine(validateDateNotInPast, '到期日期不能是过去的日期')\n    .refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),\n  discountAmount: z.number()\n    .min(0, '折扣金额不能为负数')\n    .max(999999.99, '折扣金额不能超过999,999.99')\n    .refine(validateCurrency, '折扣金额格式无效')\n    .optional(),\n  taxAmount: z.number()\n    .min(0, '税费金额不能为负数')\n    .max(999999.99, '税费金额不能超过999,999.99')\n    .refine(validateCurrency, '税费金额格式无效')\n    .optional(),\n  items: z.array(billItemSchema)\n    .min(1, '至少需要一个账单项目')\n    .max(50, '账单项目不能超过50个'),\n}).refine((data) => {\n  // Validate that bill has reasonable total\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  const taxAmount = data.taxAmount || 0;\n  const finalTotal = itemsTotal + taxAmount - discountAmount;\n  \n  return finalTotal >= 0;\n}, {\n  message: '账单总金额不能为负数',\n  path: ['discountAmount'],\n}).refine((data) => {\n  // Validate discount doesn't exceed subtotal\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  return discountAmount <= itemsTotal;\n}, {\n  message: '折扣金额不能超过项目小计',\n  path: ['discountAmount'],\n});\n\n// Payment form validation schema\nexport const paymentFormSchema = z.object({\n  amount: z.number()\n    .min(0.01, '支付金额必须大于0')\n    .max(999999.99, '支付金额不能超过999,999.99')\n    .refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),\n  paymentMethod: z.enum(['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'], {\n    required_error: '请选择支付方式',\n    invalid_type_error: '无效的支付方式',\n  }),\n  transactionId: z.string()\n    .max(100, '交易ID不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  notes: optionalString.max(500, '备注不能超过500个字符').optional(),\n}).refine((data) => {\n  // Require transaction ID for certain payment methods\n  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];\n  if (methodsRequiringTransactionId.includes(data.paymentMethod)) {\n    return data.transactionId && data.transactionId.trim().length > 0;\n  }\n  return true;\n}, {\n  message: '此支付方式需要提供交易ID',\n  path: ['transactionId'],\n});\n\n// Patient form validation schema (for billing context)\nexport const patientFormSchema = z.object({\n  fullName: requiredString\n    .min(2, '姓名至少需要2个字符')\n    .max(50, '姓名不能超过50个字符')\n    .regex(/^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/, '姓名只能包含中文、英文和空格'),\n  phone: requiredString\n    .regex(phoneRegex, '请输入有效的手机号码'),\n  email: z.string()\n    .email('请输入有效的邮箱地址')\n    .max(100, '邮箱地址不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符').optional(),\n});\n\n// Bill status update validation schema\nexport const billStatusUpdateSchema = z.object({\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled'], {\n    required_error: '请选择账单状态',\n    invalid_type_error: '无效的账单状态',\n  }),\n  notes: optionalString.max(500, '状态更新备注不能超过500个字符').optional(),\n});\n\n// Filter validation schema\nexport const billFilterSchema = z.object({\n  search: optionalString.max(100, '搜索关键词不能超过100个字符').optional(),\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled']).optional(),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional']).optional(),\n  patientId: optionalString.uuid('请选择有效的患者').optional().or(z.literal('')),\n  dateFrom: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的开始日期'),\n  dateTo: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的结束日期'),\n  amountMin: z.number()\n    .min(0, '最小金额不能为负数')\n    .max(999999.99, '最小金额不能超过999,999.99')\n    .optional(),\n  amountMax: z.number()\n    .min(0, '最大金额不能为负数')\n    .max(999999.99, '最大金额不能超过999,999.99')\n    .optional(),\n}).refine((data) => {\n  // Validate date range\n  if (data.dateFrom && data.dateTo) {\n    const fromDate = new Date(data.dateFrom);\n    const toDate = new Date(data.dateTo);\n    return fromDate <= toDate;\n  }\n  return true;\n}, {\n  message: '开始日期不能晚于结束日期',\n  path: ['dateTo'],\n}).refine((data) => {\n  // Validate amount range\n  if (data.amountMin !== undefined && data.amountMax !== undefined) {\n    return data.amountMin <= data.amountMax;\n  }\n  return true;\n}, {\n  message: '最小金额不能大于最大金额',\n  path: ['amountMax'],\n});\n\n// Export types for TypeScript\nexport type BillItemFormData = z.infer<typeof billItemSchema>;\nexport type BillFormData = z.infer<typeof billFormSchema>;\nexport type PaymentFormData = z.infer<typeof paymentFormSchema>;\nexport type PatientFormData = z.infer<typeof patientFormSchema>;\nexport type BillStatusUpdateData = z.infer<typeof billStatusUpdateSchema>;\nexport type BillFilterData = z.infer<typeof billFilterSchema>;\n\n// Validation helper functions\nexport const validateBillItem = (data: unknown) => {\n  return billItemSchema.safeParse(data);\n};\n\nexport const validateBillForm = (data: unknown) => {\n  return billFormSchema.safeParse(data);\n};\n\nexport const validatePaymentForm = (data: unknown) => {\n  return paymentFormSchema.safeParse(data);\n};\n\nexport const validatePatientForm = (data: unknown) => {\n  return patientFormSchema.safeParse(data);\n};\n\nexport const validateBillStatusUpdate = (data: unknown) => {\n  return billStatusUpdateSchema.safeParse(data);\n};\n\nexport const validateBillFilter = (data: unknown) => {\n  return billFilterSchema.safeParse(data);\n};\n\n// Custom validation error formatter\nexport const formatValidationErrors = (errors: z.ZodError) => {\n  return errors.errors.map(error => ({\n    field: error.path.join('.'),\n    message: error.message,\n    code: error.code,\n  }));\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,iFAAiF;;;;;;;;;;;;;;;;AAEjF;;AAEA,6BAA6B;AAC7B,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AACzC,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AACzC,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD;AAC9B,MAAM,aAAa;AACnB,MAAM,aAAa;AAEnB,8BAA8B;AAC9B,MAAM,mBAAmB,CAAC;IACxB,IAAI,QAAQ,GAAG,OAAO;IACtB,8CAA8C;IAC9C,MAAM,gBAAgB,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;IACnE,OAAO,iBAAiB;AAC1B;AAEA,MAAM,wBAAwB,CAAC;IAC7B,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IACxB,OAAO,aAAa;AACtB;AAEA,MAAM,gCAAgC,CAAC;IACrC,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,UAAU,IAAI;IACpB,QAAQ,WAAW,CAAC,QAAQ,WAAW,KAAK,IAAI,wBAAwB;IACxE,OAAO,aAAa;AACtB;AAGO,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACrC,UAAU,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAY;KAAU,EAAE;QACrE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,UAAU,eAAe,GAAG,CAAC,KAAK;IAClC,aAAa,eAAe,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IAC7D,UAAU,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACd,GAAG,CAAC,MAAM,WACV,GAAG,CAAC,MAAM,cACV,MAAM,CAAC,CAAC;QACP,MAAM,gBAAgB,CAAC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;QACjE,OAAO,iBAAiB;IAC1B,GAAG;IACL,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,WAAW,oBACf,MAAM,CAAC,kBAAkB;IAC5B,cAAc,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IAClB,GAAG,CAAC,GAAG,YACP,GAAG,CAAC,KAAK,eACT,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC;IACT,0CAA0C;IAC1C,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,KAAK,KAAK,SAAS,KAAK,GAAG;QACtE,OAAO;IACT;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAe;AACxB;AAGO,MAAM,iBAAiB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACrC,SAAS,eAAe,IAAI,CAAC;IAC7B,aAAa,eAAe,IAAI,CAAC,YAAY,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IACrE,WAAW,eAAe,IAAI,CAAC,cAAc,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IACrE,UAAU,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAW;KAAa,EAAE;QACvE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,aAAa,eACV,GAAG,CAAC,GAAG,gBACP,GAAG,CAAC,KAAK;IACZ,OAAO,eAAe,GAAG,CAAC,MAAM,iBAAiB,QAAQ;IACzD,SAAS,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACb,GAAG,CAAC,GAAG,WACP,MAAM,CAAC,CAAC;QACP,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG,YACF,MAAM,CAAC,uBAAuB,gBAC9B,MAAM,CAAC,+BAA+B;IACzC,gBAAgB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACpB,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB,YACzB,QAAQ;IACX,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB,YACzB,QAAQ;IACX,OAAO,CAAA,GAAA,uLAAA,CAAA,QAAO,AAAD,EAAE,gBACZ,GAAG,CAAC,GAAG,cACP,GAAG,CAAC,IAAI;AACb,GAAG,MAAM,CAAC,CAAC;IACT,0CAA0C;IAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;QAChD,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;QAChE,OAAO,MAAM,CAAC,YAAY,YAAY;IACxC,GAAG;IAEH,MAAM,iBAAiB,KAAK,cAAc,IAAI;IAC9C,MAAM,YAAY,KAAK,SAAS,IAAI;IACpC,MAAM,aAAa,aAAa,YAAY;IAE5C,OAAO,cAAc;AACvB,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAiB;AAC1B,GAAG,MAAM,CAAC,CAAC;IACT,4CAA4C;IAC5C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;QAChD,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;QAChE,OAAO,MAAM,CAAC,YAAY,YAAY;IACxC,GAAG;IAEH,MAAM,iBAAiB,KAAK,cAAc,IAAI;IAC9C,OAAO,kBAAkB;AAC3B,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAiB;AAC1B;AAGO,MAAM,oBAAoB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACxC,QAAQ,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACZ,GAAG,CAAC,MAAM,aACV,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB;IAC5B,eAAe,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAQ;QAAQ;QAAU;QAAU;QAAY;KAAc,EAAE;QACrF,gBAAgB;QAChB,oBAAoB;IACtB;IACA,eAAe,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACnB,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IAChB,OAAO,eAAe,GAAG,CAAC,KAAK,gBAAgB,QAAQ;AACzD,GAAG,MAAM,CAAC,CAAC;IACT,qDAAqD;IACrD,MAAM,gCAAgC;QAAC;QAAQ;QAAU;QAAU;KAAW;IAC9E,IAAI,8BAA8B,QAAQ,CAAC,KAAK,aAAa,GAAG;QAC9D,OAAO,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,IAAI,GAAG,MAAM,GAAG;IAClE;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAgB;AACzB;AAGO,MAAM,oBAAoB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACxC,UAAU,eACP,GAAG,CAAC,GAAG,cACP,GAAG,CAAC,IAAI,eACR,KAAK,CAAC,8BAA8B;IACvC,OAAO,eACJ,KAAK,CAAC,YAAY;IACrB,OAAO,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACX,KAAK,CAAC,cACN,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,eAAe,GAAG,CAAC,MAAM,mBAAmB,QAAQ;AACpE;AAGO,MAAM,yBAAyB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IAC7C,QAAQ,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;QAAQ;QAAa;QAAQ;KAAY,EAAE;QAClE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,OAAO,eAAe,GAAG,CAAC,KAAK,oBAAoB,QAAQ;AAC7D;AAGO,MAAM,mBAAmB,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,EAAE;IACvC,QAAQ,eAAe,GAAG,CAAC,KAAK,mBAAmB,QAAQ;IAC3D,QAAQ,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;QAAQ;QAAa;QAAQ;KAAY,EAAE,QAAQ;IAC5E,UAAU,CAAA,GAAA,uLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAW;KAAa,EAAE,QAAQ;IACjF,WAAW,eAAe,IAAI,CAAC,YAAY,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uLAAA,CAAA,UAAS,AAAD,EAAE;IACnE,UAAU,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACd,QAAQ,GACR,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG;IACL,QAAQ,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACZ,QAAQ,GACR,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG;IACL,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,QAAQ;IACX,WAAW,CAAA,GAAA,uLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC;IACT,sBAAsB;IACtB,IAAI,KAAK,QAAQ,IAAI,KAAK,MAAM,EAAE;QAChC,MAAM,WAAW,IAAI,KAAK,KAAK,QAAQ;QACvC,MAAM,SAAS,IAAI,KAAK,KAAK,MAAM;QACnC,OAAO,YAAY;IACrB;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAS;AAClB,GAAG,MAAM,CAAC,CAAC;IACT,wBAAwB;IACxB,IAAI,KAAK,SAAS,KAAK,aAAa,KAAK,SAAS,KAAK,WAAW;QAChE,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS;IACzC;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAY;AACrB;AAWO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,SAAS,CAAC;AAClC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,SAAS,CAAC;AAClC;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,SAAS,CAAC;AACrC;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,SAAS,CAAC;AACrC;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,uBAAuB,SAAS,CAAC;AAC1C;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,iBAAiB,SAAS,CAAC;AACpC;AAGO,MAAM,yBAAyB,CAAC;IACrC,OAAO,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;YACjC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC;YACvB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;QAClB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/billing-error-handler.ts"], "sourcesContent": ["// Comprehensive error handling system for billing operations\n// Provides consistent error handling, logging, and user feedback\n\nimport { toast } from 'sonner';\n\n// Error types and interfaces\nexport interface BillingError {\n  code: string;\n  message: string;\n  userMessage: string;\n  severity: 'error' | 'warning' | 'info';\n  details?: any;\n  timestamp: Date;\n  context?: string;\n}\n\nexport interface ErrorHandlerOptions {\n  showToast?: boolean;\n  logError?: boolean;\n  context?: string;\n  fallbackMessage?: string;\n}\n\n// Error code mappings with user-friendly messages\nconst ERROR_MESSAGES: Record<string, { message: string; severity: 'error' | 'warning' | 'info' }> = {\n  // Authentication errors\n  AUTH_REQUIRED: { message: '请先登录以继续操作', severity: 'error' },\n  AUTH_SERVICE_ERROR: { message: '认证服务暂时不可用，请稍后重试', severity: 'error' },\n  CLERK_USER_FETCH_ERROR: { message: '获取用户信息失败，请重新登录', severity: 'error' },\n  USER_EMAIL_MISSING: { message: '用户邮箱信息缺失，请联系管理员', severity: 'error' },\n\n  // Validation errors\n  VALIDATION_ERROR: { message: '输入数据验证失败，请检查表单内容', severity: 'error' },\n  INVALID_JSON: { message: '数据格式错误，请刷新页面重试', severity: 'error' },\n  INVALID_LIMIT: { message: '分页参数错误', severity: 'warning' },\n  INVALID_PAGE: { message: '页码参数错误', severity: 'warning' },\n  SUBTOTAL_MISMATCH: { message: '账单金额计算错误，请重新检查', severity: 'error' },\n\n  // Business logic errors\n  INVALID_PAYMENT_AMOUNT: { message: '支付金额无效', severity: 'error' },\n  BILL_NOT_FOUND: { message: '账单不存在或已被删除', severity: 'error' },\n  PAYMENT_METHOD_ERROR: { message: '支付方式验证失败', severity: 'error' },\n  INSUFFICIENT_PERMISSIONS: { message: '权限不足，无法执行此操作', severity: 'error' },\n\n  // Backend/Service errors\n  BACKEND_ERROR: { message: '后端服务错误', severity: 'error' },\n  BACKEND_SERVICE_ERROR: { message: '后端服务暂时不可用，请稍后重试', severity: 'error' },\n  DATABASE_ERROR: { message: '数据库操作失败，请稍后重试', severity: 'error' },\n  NETWORK_ERROR: { message: '网络连接错误，请检查网络连接', severity: 'error' },\n\n  // Generic errors\n  INTERNAL_ERROR: { message: '系统内部错误，请稍后重试', severity: 'error' },\n  UNKNOWN_ERROR: { message: '发生未知错误，请联系技术支持', severity: 'error' },\n};\n\n// Error handler class\nexport class BillingErrorHandler {\n  private static instance: BillingErrorHandler;\n  private errorLog: BillingError[] = [];\n\n  private constructor() {}\n\n  static getInstance(): BillingErrorHandler {\n    if (!BillingErrorHandler.instance) {\n      BillingErrorHandler.instance = new BillingErrorHandler();\n    }\n    return BillingErrorHandler.instance;\n  }\n\n  /**\n   * Handle API response errors\n   */\n  handleAPIError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const {\n      showToast = true,\n      logError = true,\n      context = 'API',\n      fallbackMessage = '操作失败，请稍后重试'\n    } = options;\n\n    let billingError: BillingError;\n\n    if (error?.code && ERROR_MESSAGES[error.code]) {\n      const errorInfo = ERROR_MESSAGES[error.code];\n      billingError = {\n        code: error.code,\n        message: error.message || errorInfo.message,\n        userMessage: error.message || errorInfo.message,\n        severity: errorInfo.severity,\n        details: error.details,\n        timestamp: new Date(),\n        context\n      };\n    } else {\n      // Handle unknown errors\n      billingError = {\n        code: 'UNKNOWN_ERROR',\n        message: error?.message || 'Unknown error occurred',\n        userMessage: fallbackMessage,\n        severity: 'error',\n        details: error,\n        timestamp: new Date(),\n        context\n      };\n    }\n\n    if (logError) {\n      this.logError(billingError);\n    }\n\n    if (showToast) {\n      this.showErrorToast(billingError);\n    }\n\n    return billingError;\n  }\n\n  /**\n   * Handle network/fetch errors\n   */\n  handleNetworkError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const networkError: BillingError = {\n      code: 'NETWORK_ERROR',\n      message: error?.message || 'Network request failed',\n      userMessage: '网络连接错误，请检查网络连接后重试',\n      severity: 'error',\n      details: error,\n      timestamp: new Date(),\n      context: options.context || 'Network'\n    };\n\n    if (options.logError !== false) {\n      this.logError(networkError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(networkError);\n    }\n\n    return networkError;\n  }\n\n  /**\n   * Handle validation errors\n   */\n  handleValidationError(validationErrors: any[], options: ErrorHandlerOptions = {}): BillingError {\n    const firstError = validationErrors[0];\n    const validationError: BillingError = {\n      code: 'VALIDATION_ERROR',\n      message: 'Validation failed',\n      userMessage: firstError?.message || '表单验证失败，请检查输入内容',\n      severity: 'error',\n      details: validationErrors,\n      timestamp: new Date(),\n      context: options.context || 'Validation'\n    };\n\n    if (options.logError !== false) {\n      this.logError(validationError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(validationError);\n    }\n\n    return validationError;\n  }\n\n  /**\n   * Show success message\n   */\n  showSuccess(message: string, description?: string): void {\n    toast.success(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Show warning message\n   */\n  showWarning(message: string, description?: string): void {\n    toast.warning(message, {\n      description,\n      duration: 4000,\n    });\n  }\n\n  /**\n   * Show info message\n   */\n  showInfo(message: string, description?: string): void {\n    toast.info(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Log error to console and internal log\n   */\n  private logError(error: BillingError): void {\n    console.error(`[${error.context}] ${error.code}: ${error.message}`, {\n      userMessage: error.userMessage,\n      details: error.details,\n      timestamp: error.timestamp\n    });\n\n    // Add to internal error log (keep last 100 errors)\n    this.errorLog.push(error);\n    if (this.errorLog.length > 100) {\n      this.errorLog.shift();\n    }\n  }\n\n  /**\n   * Show error toast notification\n   */\n  private showErrorToast(error: BillingError): void {\n    const toastOptions = {\n      duration: error.severity === 'error' ? 5000 : 4000,\n    };\n\n    switch (error.severity) {\n      case 'error':\n        toast.error(error.userMessage, toastOptions);\n        break;\n      case 'warning':\n        toast.warning(error.userMessage, toastOptions);\n        break;\n      case 'info':\n        toast.info(error.userMessage, toastOptions);\n        break;\n    }\n  }\n\n  /**\n   * Get error log for debugging\n   */\n  getErrorLog(): BillingError[] {\n    return [...this.errorLog];\n  }\n\n  /**\n   * Clear error log\n   */\n  clearErrorLog(): void {\n    this.errorLog = [];\n  }\n}\n\n// Convenience functions for common error handling patterns\nexport const billingErrorHandler = BillingErrorHandler.getInstance();\n\nexport const handleAPIError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleAPIError(error, options);\n\nexport const handleNetworkError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleNetworkError(error, options);\n\nexport const handleValidationError = (errors: any[], options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleValidationError(errors, options);\n\nexport const showSuccess = (message: string, description?: string) => \n  billingErrorHandler.showSuccess(message, description);\n\nexport const showWarning = (message: string, description?: string) => \n  billingErrorHandler.showWarning(message, description);\n\nexport const showInfo = (message: string, description?: string) => \n  billingErrorHandler.showInfo(message, description);\n\n// Error boundary helper for React components\nexport const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(\n  fn: T,\n  context?: string\n): T => {\n  return (async (...args: any[]) => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      handleAPIError(error, { context });\n      throw error;\n    }\n  }) as T;\n};\n\n// Retry utility with exponential backoff\nexport const retryWithBackoff = async <T>(\n  fn: () => Promise<T>,\n  maxRetries: number = 3,\n  baseDelay: number = 1000,\n  context?: string\n): Promise<T> => {\n  let lastError: any;\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      return await fn();\n    } catch (error) {\n      lastError = error;\n      \n      if (attempt === maxRetries) {\n        handleAPIError(error, { \n          context: context || 'Retry',\n          fallbackMessage: `操作失败，已重试${maxRetries}次`\n        });\n        throw error;\n      }\n\n      // Exponential backoff delay\n      const delay = baseDelay * Math.pow(2, attempt - 1);\n      await new Promise(resolve => setTimeout(resolve, delay));\n    }\n  }\n\n  throw lastError;\n};\n"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,iEAAiE;;;;;;;;;;;;;AAEjE;;AAoBA,kDAAkD;AAClD,MAAM,iBAA8F;IAClG,wBAAwB;IACxB,eAAe;QAAE,SAAS;QAAa,UAAU;IAAQ;IACzD,oBAAoB;QAAE,SAAS;QAAmB,UAAU;IAAQ;IACpE,wBAAwB;QAAE,SAAS;QAAkB,UAAU;IAAQ;IACvE,oBAAoB;QAAE,SAAS;QAAmB,UAAU;IAAQ;IAEpE,oBAAoB;IACpB,kBAAkB;QAAE,SAAS;QAAoB,UAAU;IAAQ;IACnE,cAAc;QAAE,SAAS;QAAkB,UAAU;IAAQ;IAC7D,eAAe;QAAE,SAAS;QAAU,UAAU;IAAU;IACxD,cAAc;QAAE,SAAS;QAAU,UAAU;IAAU;IACvD,mBAAmB;QAAE,SAAS;QAAkB,UAAU;IAAQ;IAElE,wBAAwB;IACxB,wBAAwB;QAAE,SAAS;QAAU,UAAU;IAAQ;IAC/D,gBAAgB;QAAE,SAAS;QAAc,UAAU;IAAQ;IAC3D,sBAAsB;QAAE,SAAS;QAAY,UAAU;IAAQ;IAC/D,0BAA0B;QAAE,SAAS;QAAgB,UAAU;IAAQ;IAEvE,yBAAyB;IACzB,eAAe;QAAE,SAAS;QAAU,UAAU;IAAQ;IACtD,uBAAuB;QAAE,SAAS;QAAmB,UAAU;IAAQ;IACvE,gBAAgB;QAAE,SAAS;QAAiB,UAAU;IAAQ;IAC9D,eAAe;QAAE,SAAS;QAAkB,UAAU;IAAQ;IAE9D,iBAAiB;IACjB,gBAAgB;QAAE,SAAS;QAAgB,UAAU;IAAQ;IAC7D,eAAe;QAAE,SAAS;QAAkB,UAAU;IAAQ;AAChE;AAGO,MAAM;IACX,OAAe,SAA8B;IACrC,WAA2B,EAAE,CAAC;IAEtC,aAAsB,CAAC;IAEvB,OAAO,cAAmC;QACxC,IAAI,CAAC,oBAAoB,QAAQ,EAAE;YACjC,oBAAoB,QAAQ,GAAG,IAAI;QACrC;QACA,OAAO,oBAAoB,QAAQ;IACrC;IAEA;;GAEC,GACD,eAAe,KAAU,EAAE,UAA+B,CAAC,CAAC,EAAgB;QAC1E,MAAM,EACJ,YAAY,IAAI,EAChB,WAAW,IAAI,EACf,UAAU,KAAK,EACf,kBAAkB,YAAY,EAC/B,GAAG;QAEJ,IAAI;QAEJ,IAAI,OAAO,QAAQ,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE;YAC7C,MAAM,YAAY,cAAc,CAAC,MAAM,IAAI,CAAC;YAC5C,eAAe;gBACb,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO,IAAI,UAAU,OAAO;gBAC3C,aAAa,MAAM,OAAO,IAAI,UAAU,OAAO;gBAC/C,UAAU,UAAU,QAAQ;gBAC5B,SAAS,MAAM,OAAO;gBACtB,WAAW,IAAI;gBACf;YACF;QACF,OAAO;YACL,wBAAwB;YACxB,eAAe;gBACb,MAAM;gBACN,SAAS,OAAO,WAAW;gBAC3B,aAAa;gBACb,UAAU;gBACV,SAAS;gBACT,WAAW,IAAI;gBACf;YACF;QACF;QAEA,IAAI,UAAU;YACZ,IAAI,CAAC,QAAQ,CAAC;QAChB;QAEA,IAAI,WAAW;YACb,IAAI,CAAC,cAAc,CAAC;QACtB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,KAAU,EAAE,UAA+B,CAAC,CAAC,EAAgB;QAC9E,MAAM,eAA6B;YACjC,MAAM;YACN,SAAS,OAAO,WAAW;YAC3B,aAAa;YACb,UAAU;YACV,SAAS;YACT,WAAW,IAAI;YACf,SAAS,QAAQ,OAAO,IAAI;QAC9B;QAEA,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC9B,IAAI,CAAC,QAAQ,CAAC;QAChB;QAEA,IAAI,QAAQ,SAAS,KAAK,OAAO;YAC/B,IAAI,CAAC,cAAc,CAAC;QACtB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,sBAAsB,gBAAuB,EAAE,UAA+B,CAAC,CAAC,EAAgB;QAC9F,MAAM,aAAa,gBAAgB,CAAC,EAAE;QACtC,MAAM,kBAAgC;YACpC,MAAM;YACN,SAAS;YACT,aAAa,YAAY,WAAW;YACpC,UAAU;YACV,SAAS;YACT,WAAW,IAAI;YACf,SAAS,QAAQ,OAAO,IAAI;QAC9B;QAEA,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC9B,IAAI,CAAC,QAAQ,CAAC;QAChB;QAEA,IAAI,QAAQ,SAAS,KAAK,OAAO;YAC/B,IAAI,CAAC,cAAc,CAAC;QACtB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,YAAY,OAAe,EAAE,WAAoB,EAAQ;QACvD,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS;YACrB;YACA,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,YAAY,OAAe,EAAE,WAAoB,EAAQ;QACvD,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS;YACrB;YACA,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,SAAS,OAAe,EAAE,WAAoB,EAAQ;QACpD,0QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,SAAS;YAClB;YACA,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,AAAQ,SAAS,KAAmB,EAAQ;QAC1C,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE,EAAE;YAClE,aAAa,MAAM,WAAW;YAC9B,SAAS,MAAM,OAAO;YACtB,WAAW,MAAM,SAAS;QAC5B;QAEA,mDAAmD;QACnD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK;YAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,eAAe,KAAmB,EAAQ;QAChD,MAAM,eAAe;YACnB,UAAU,MAAM,QAAQ,KAAK,UAAU,OAAO;QAChD;QAEA,OAAQ,MAAM,QAAQ;YACpB,KAAK;gBACH,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,WAAW,EAAE;gBAC/B;YACF,KAAK;gBACH,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,MAAM,WAAW,EAAE;gBACjC;YACF,KAAK;gBACH,0QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,MAAM,WAAW,EAAE;gBAC9B;QACJ;IACF;IAEA;;GAEC,GACD,cAA8B;QAC5B,OAAO;eAAI,IAAI,CAAC,QAAQ;SAAC;IAC3B;IAEA;;GAEC,GACD,gBAAsB;QACpB,IAAI,CAAC,QAAQ,GAAG,EAAE;IACpB;AACF;AAGO,MAAM,sBAAsB,oBAAoB,WAAW;AAE3D,MAAM,iBAAiB,CAAC,OAAY,UACzC,oBAAoB,cAAc,CAAC,OAAO;AAErC,MAAM,qBAAqB,CAAC,OAAY,UAC7C,oBAAoB,kBAAkB,CAAC,OAAO;AAEzC,MAAM,wBAAwB,CAAC,QAAe,UACnD,oBAAoB,qBAAqB,CAAC,QAAQ;AAE7C,MAAM,cAAc,CAAC,SAAiB,cAC3C,oBAAoB,WAAW,CAAC,SAAS;AAEpC,MAAM,cAAc,CAAC,SAAiB,cAC3C,oBAAoB,WAAW,CAAC,SAAS;AAEpC,MAAM,WAAW,CAAC,SAAiB,cACxC,oBAAoB,QAAQ,CAAC,SAAS;AAGjC,MAAM,oBAAoB,CAC/B,IACA;IAEA,OAAQ,OAAO,GAAG;QAChB,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,eAAe,OAAO;gBAAE;YAAQ;YAChC,MAAM;QACR;IACF;AACF;AAGO,MAAM,mBAAmB,OAC9B,IACA,aAAqB,CAAC,EACtB,YAAoB,IAAI,EACxB;IAEA,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,IAAI,YAAY,YAAY;gBAC1B,eAAe,OAAO;oBACpB,SAAS,WAAW;oBACpB,iBAAiB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAC3C;gBACA,MAAM;YACR;YAEA,4BAA4B;YAC5B,MAAM,QAAQ,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU;YAChD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,MAAM;AACR", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/api/billing.ts"], "sourcesContent": ["// Billing API client functions for medical clinic system\n// Handles bills, payments, and financial operations with comprehensive error handling\n\nimport { Bill, Payment, BillItem, PayloadResponse } from '@/types/clinic';\nimport {\n  handleAPIError,\n  handleNetworkError,\n  retryWithBackoff,\n  BillingError\n} from '@/lib/billing-error-handler';\n\n// API base URL - using relative paths for Next.js API routes\nconst API_BASE = '/api';\n\n// Enhanced error handling utility\nexport class BillingAPIError extends Error {\n  constructor(\n    message: string,\n    public status?: number,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'BillingAPIError';\n  }\n}\n\n// Enhanced API request handler with comprehensive error handling and retry logic\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {},\n  retryOptions?: { maxRetries?: number; context?: string }\n): Promise<T> {\n  const url = `${API_BASE}${endpoint}`;\n\n  const defaultHeaders = {\n    'Content-Type': 'application/json',\n  };\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      ...defaultHeaders,\n      ...options.headers,\n    },\n  };\n\n  const makeRequest = async (): Promise<T> => {\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        let errorData;\n        try {\n          errorData = await response.json();\n        } catch {\n          errorData = {\n            error: `HTTP ${response.status}: ${response.statusText}`,\n            code: `HTTP_${response.status}`,\n            message: response.statusText\n          };\n        }\n\n        const apiError = new BillingAPIError(\n          errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData.code || `HTTP_${response.status}`,\n          errorData.details\n        );\n\n        // Handle the error through the error handler\n        handleAPIError(errorData, {\n          context: retryOptions?.context || 'API Request',\n          showToast: false // Don't show toast here, let the calling function decide\n        });\n\n        throw apiError;\n      }\n\n      return await response.json();\n    } catch (error) {\n      if (error instanceof BillingAPIError) {\n        throw error;\n      }\n\n      // Handle network errors\n      const networkError = new BillingAPIError(\n        error instanceof Error ? error.message : 'Network error occurred',\n        0,\n        'NETWORK_ERROR',\n        error\n      );\n\n      handleNetworkError(error, {\n        context: retryOptions?.context || 'API Request',\n        showToast: false\n      });\n\n      throw networkError;\n    }\n  };\n\n  // Use retry logic for GET requests and other idempotent operations\n  const isIdempotent = !options.method || ['GET', 'HEAD', 'OPTIONS'].includes(options.method.toUpperCase());\n\n  if (isIdempotent && retryOptions?.maxRetries) {\n    return retryWithBackoff(\n      makeRequest,\n      retryOptions.maxRetries,\n      1000,\n      retryOptions.context\n    );\n  }\n\n  return makeRequest();\n}\n\n// Bill API Functions\nexport const billsAPI = {\n  /**\n   * Fetch all bills with optional filtering and pagination\n   */\n  async fetchBills(params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n    status?: string;\n    patientId?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Bill>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n    if (params?.status) searchParams.append('status', params.status);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/bills${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Bill>>(\n      endpoint,\n      {},\n      { maxRetries: 3, context: 'Fetch Bills' }\n    );\n  },\n\n  /**\n   * Fetch a specific bill by ID\n   */\n  async fetchBill(id: string): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`);\n  },\n\n  /**\n   * Create a new bill\n   */\n  async createBill(billData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    billType: 'treatment' | 'consultation' | 'deposit' | 'additional';\n    subtotal: number;\n    discountAmount?: number;\n    taxAmount?: number;\n    totalAmount: number;\n    description: string;\n    notes?: string;\n    dueDate: string;\n    items?: Array<{\n      itemType: 'treatment' | 'consultation' | 'material' | 'service';\n      itemName: string;\n      description?: string;\n      quantity: number;\n      unitPrice: number;\n      discountRate?: number;\n    }>;\n  }): Promise<Bill> {\n    return apiRequest<Bill>('/bills', {\n      method: 'POST',\n      body: JSON.stringify(billData),\n    });\n  },\n\n  /**\n   * Update an existing bill\n   */\n  async updateBill(id: string, updateData: Partial<Bill>): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Delete a bill\n   */\n  async deleteBill(id: string): Promise<void> {\n    return apiRequest<void>(`/bills/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  /**\n   * Generate bill from appointment\n   */\n  async generateFromAppointment(appointmentId: string, billType: string = 'treatment'): Promise<Bill> {\n    return apiRequest<Bill>('/bills/generate-from-appointment', {\n      method: 'POST',\n      body: JSON.stringify({ appointmentId, billType }),\n    });\n  },\n\n  /**\n   * Check if appointment already has a bill\n   */\n  async checkAppointmentBill(appointmentId: string): Promise<{ hasBill: boolean; bill?: Bill }> {\n    try {\n      const response = await billsAPI.fetchBills({\n        limit: 1,\n        // Note: This would need backend support for filtering by appointment\n        // For now, we'll fetch and filter client-side in components\n      });\n\n      const bill = response.docs.find(bill =>\n        typeof bill.appointment === 'object' && bill.appointment?.id === appointmentId\n      );\n\n      return {\n        hasBill: !!bill,\n        bill: bill || undefined,\n      };\n    } catch (error) {\n      console.error('Failed to check appointment bill:', error);\n      return { hasBill: false };\n    }\n  },\n};\n\n// Payment API Functions\nexport const paymentsAPI = {\n  /**\n   * Fetch all payments with optional filtering\n   */\n  async fetchPayments(params?: {\n    page?: number;\n    limit?: number;\n    billId?: string;\n    patientId?: string;\n    paymentMethod?: string;\n    status?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Payment>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.billId) searchParams.append('bill', params.billId);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.paymentMethod) searchParams.append('paymentMethod', params.paymentMethod);\n    if (params?.status) searchParams.append('paymentStatus', params.status);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/payments${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Payment>>(endpoint);\n  },\n\n  /**\n   * Fetch a specific payment by ID\n   */\n  async fetchPayment(id: string): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`);\n  },\n\n  /**\n   * Process a new payment\n   */\n  async processPayment(paymentData: {\n    bill: string;\n    patient: string;\n    amount: number;\n    paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';\n    transactionId?: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>('/payments', {\n      method: 'POST',\n      body: JSON.stringify(paymentData),\n    });\n  },\n\n  /**\n   * Update payment status\n   */\n  async updatePayment(id: string, updateData: Partial<Payment>): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Process refund\n   */\n  async processRefund(paymentId: string, refundData: {\n    amount: number;\n    reason: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${paymentId}/refund`, {\n      method: 'POST',\n      body: JSON.stringify(refundData),\n    });\n  },\n};\n\n// Financial reporting API functions\nexport const reportsAPI = {\n  /**\n   * Get daily revenue report\n   */\n  async getDailyRevenue(date: string): Promise<{\n    date: string;\n    totalRevenue: number;\n    paymentCount: number;\n    paymentMethods: Record<string, { amount: number; count: number }>;\n  }> {\n    return apiRequest(`/reports/daily-revenue?date=${date}`);\n  },\n\n  /**\n   * Get monthly revenue report\n   */\n  async getMonthlyRevenue(year: number, month: number): Promise<{\n    year: number;\n    month: number;\n    totalRevenue: number;\n    dailyBreakdown: Array<{ date: string; revenue: number }>;\n  }> {\n    return apiRequest(`/reports/monthly-revenue?year=${year}&month=${month}`);\n  },\n\n  /**\n   * Get outstanding balances report\n   */\n  async getOutstandingBalances(): Promise<{\n    totalOutstanding: number;\n    overdueAmount: number;\n    billsCount: number;\n    overdueBillsCount: number;\n    bills: Array<{\n      id: string;\n      billNumber: string;\n      patient: string;\n      amount: number;\n      dueDate: string;\n      daysOverdue: number;\n    }>;\n  }> {\n    return apiRequest('/reports/outstanding-balances');\n  },\n\n  /**\n   * Generate financial report\n   */\n  async getFinancialReport(params?: {\n    startDate?: string;\n    endDate?: string;\n    type?: 'summary' | 'detailed';\n  }): Promise<any> {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    if (params?.type) searchParams.set('type', params.type);\n\n    return apiRequest(`/reports/financial?${searchParams.toString()}`);\n  },\n};\n\n// Deposit API functions\nexport const depositsAPI = {\n  /**\n   * Create a new deposit\n   */\n  async createDeposit(depositData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    depositType: 'treatment' | 'appointment' | 'material';\n    amount: number;\n    purpose: string;\n    notes?: string;\n    expiryDate?: string;\n  }): Promise<any> {\n    return apiRequest('/deposits', {\n      method: 'POST',\n      body: JSON.stringify(depositData),\n    });\n  },\n\n  /**\n   * Get deposits with filtering and pagination\n   */\n  async getDeposits(params?: {\n    page?: number;\n    limit?: number;\n    patient?: string;\n    status?: string;\n    depositType?: string;\n  }): Promise<PayloadResponse<any>> {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n    if (params?.patient) searchParams.set('where[patient][equals]', params.patient);\n    if (params?.status) searchParams.set('where[status][equals]', params.status);\n    if (params?.depositType) searchParams.set('where[depositType][equals]', params.depositType);\n\n    return apiRequest(`/deposits?${searchParams.toString()}`);\n  },\n\n  /**\n   * Get deposit by ID\n   */\n  async getDepositById(id: string): Promise<any> {\n    return apiRequest(`/deposits/${id}`);\n  },\n\n  /**\n   * Update deposit\n   */\n  async updateDeposit(id: string, updateData: {\n    status?: string;\n    usedAmount?: number;\n    notes?: string;\n  }): Promise<any> {\n    return apiRequest(`/deposits/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Apply deposit to bill\n   */\n  async applyToBill(depositId: string, billId: string, amount: number): Promise<any> {\n    return apiRequest('/deposits/apply-to-bill', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        billId,\n        amount,\n      }),\n    });\n  },\n\n  /**\n   * Process deposit refund\n   */\n  async processRefund(depositId: string, refundAmount: number, refundReason: string, refundMethod: string = 'cash'): Promise<any> {\n    return apiRequest('/deposits/refund', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        refundAmount,\n        refundReason,\n        refundMethod,\n      }),\n    });\n  },\n};\n\n// Receipt API functions\nexport const receiptsAPI = {\n  /**\n   * Generate receipt for payment\n   */\n  async generateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`);\n  },\n\n  /**\n   * Regenerate receipt number (admin only)\n   */\n  async regenerateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`, {\n      method: 'POST',\n    });\n  },\n};\n\n\n\n// Export the error class for use in components\nexport { BillingAPIError };\n\n// Export utility functions\nexport const billingUtils = {\n  /**\n   * Format currency amount for display\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  },\n\n  /**\n   * Calculate bill total with discounts and taxes\n   */\n  calculateBillTotal(subtotal: number, discountAmount: number = 0, taxAmount: number = 0): number {\n    return subtotal + taxAmount - discountAmount;\n  },\n\n  /**\n   * Get payment method display name\n   */\n  getPaymentMethodName(method: string): string {\n    const methods: Record<string, string> = {\n      cash: '现金',\n      card: '银行卡',\n      wechat: '微信支付',\n      alipay: '支付宝',\n      transfer: '银行转账',\n      deposit: '押金抵扣',\n      installment: '分期付款',\n    };\n    return methods[method] || method;\n  },\n\n  /**\n   * Get bill status display name\n   */\n  getBillStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      draft: '草稿',\n      sent: '已发送',\n      confirmed: '已确认',\n      paid: '已支付',\n      cancelled: '已取消',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get payment status display name\n   */\n  getPaymentStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      pending: '待处理',\n      completed: '已完成',\n      failed: '失败',\n      refunded: '已退款',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get deposit status display name\n   */\n  getDepositStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      active: '有效',\n      used: '已使用',\n      refunded: '已退还',\n      expired: '已过期',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Validate payment amount against bill balance\n   */\n  validatePaymentAmount(amount: number, billBalance: number): boolean {\n    return amount > 0 && amount <= billBalance;\n  },\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,sFAAsF;;;;;;;;;;AAGtF;;AAOA,6DAA6D;AAC7D,MAAM,WAAW;AAGV,MAAM,wBAAwB;;;;IACnC,YACE,OAAe,EACf,AAAO,MAAe,EACtB,AAAO,IAAa,EACpB,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eAJC,SAAA,aACA,OAAA,WACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,iFAAiF;AACjF,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACzB,YAAwD;IAExD,MAAM,MAAM,GAAG,WAAW,UAAU;IAEpC,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,SAAS;YACP,GAAG,cAAc;YACjB,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI;gBACJ,IAAI;oBACF,YAAY,MAAM,SAAS,IAAI;gBACjC,EAAE,OAAM;oBACN,YAAY;wBACV,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;wBACxD,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;wBAC/B,SAAS,SAAS,UAAU;oBAC9B;gBACF;gBAEA,MAAM,WAAW,IAAI,gBACnB,UAAU,KAAK,IAAI,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACzF,SAAS,MAAM,EACf,UAAU,IAAI,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE,EAC3C,UAAU,OAAO;gBAGnB,6CAA6C;gBAC7C,CAAA,GAAA,2IAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;oBACxB,SAAS,cAAc,WAAW;oBAClC,WAAW,MAAM,yDAAyD;gBAC5E;gBAEA,MAAM;YACR;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,iBAAiB;gBACpC,MAAM;YACR;YAEA,wBAAwB;YACxB,MAAM,eAAe,IAAI,gBACvB,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC,GACA,iBACA;YAGF,CAAA,GAAA,2IAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;gBACxB,SAAS,cAAc,WAAW;gBAClC,WAAW;YACb;YAEA,MAAM;QACR;IACF;IAEA,mEAAmE;IACnE,MAAM,eAAe,CAAC,QAAQ,MAAM,IAAI;QAAC;QAAO;QAAQ;KAAU,CAAC,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW;IAEtG,IAAI,gBAAgB,cAAc,YAAY;QAC5C,OAAO,CAAA,GAAA,2IAAA,CAAA,mBAAgB,AAAD,EACpB,aACA,aAAa,UAAU,EACvB,MACA,aAAa,OAAO;IAExB;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB;;GAEC,GACD,MAAM,YAAW,MAQhB;QACC,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,WAAW,OAAO,SAAS;QACtE,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,WAAW,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEhE,OAAO,WACL,UACA,CAAC,GACD;YAAE,YAAY;YAAG,SAAS;QAAc;IAE5C;IAEA;;GAEC,GACD,MAAM,WAAU,EAAU;QACxB,OAAO,WAAiB,CAAC,OAAO,EAAE,IAAI;IACxC;IAEA;;GAEC,GACD,MAAM,YAAW,QAoBhB;QACC,OAAO,WAAiB,UAAU;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,YAAW,EAAU,EAAE,UAAyB;QACpD,OAAO,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,YAAW,EAAU;QACzB,OAAO,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YACtC,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,yBAAwB,aAAqB,EAAE,WAAmB,WAAW;QACjF,OAAO,WAAiB,oCAAoC;YAC1D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAe;YAAS;QACjD;IACF;IAEA;;GAEC,GACD,MAAM,sBAAqB,aAAqB;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,SAAS,UAAU,CAAC;gBACzC,OAAO;YAGT;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,OAC9B,OAAO,KAAK,WAAW,KAAK,YAAY,KAAK,WAAW,EAAE,OAAO;YAGnE,OAAO;gBACL,SAAS,CAAC,CAAC;gBACX,MAAM,QAAQ;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,SAAS;YAAM;QAC1B;IACF;AACF;AAGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,eAAc,MASnB;QACC,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,QAAQ,OAAO,MAAM;QAC7D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,WAAW,OAAO,SAAS;QACtE,IAAI,QAAQ,eAAe,aAAa,MAAM,CAAC,iBAAiB,OAAO,aAAa;QACpF,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACtE,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,WAAW,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEnE,OAAO,WAAqC;IAC9C;IAEA;;GAEC,GACD,MAAM,cAAa,EAAU;QAC3B,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI;IAC9C;IAEA;;GAEC,GACD,MAAM,gBAAe,WAOpB;QACC,OAAO,WAAoB,aAAa;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,eAAc,EAAU,EAAE,UAA4B;QAC1D,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,eAAc,SAAiB,EAAE,UAItC;QACC,OAAO,WAAoB,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC1D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,aAAa;IACxB;;GAEC,GACD,MAAM,iBAAgB,IAAY;QAMhC,OAAO,WAAW,CAAC,4BAA4B,EAAE,MAAM;IACzD;IAEA;;GAEC,GACD,MAAM,mBAAkB,IAAY,EAAE,KAAa;QAMjD,OAAO,WAAW,CAAC,8BAA8B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC1E;IAEA;;GAEC,GACD,MAAM;QAcJ,OAAO,WAAW;IACpB;IAEA;;GAEC,GACD,MAAM,oBAAmB,MAIxB;QACC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,WAAW,OAAO,OAAO;QAC/D,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QAEtD,OAAO,WAAW,CAAC,mBAAmB,EAAE,aAAa,QAAQ,IAAI;IACnE;AACF;AAGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,eAAc,WASnB;QACC,OAAO,WAAW,aAAa;YAC7B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,aAAY,MAMjB;QACC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAClE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,0BAA0B,OAAO,OAAO;QAC9E,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,yBAAyB,OAAO,MAAM;QAC3E,IAAI,QAAQ,aAAa,aAAa,GAAG,CAAC,8BAA8B,OAAO,WAAW;QAE1F,OAAO,WAAW,CAAC,UAAU,EAAE,aAAa,QAAQ,IAAI;IAC1D;IAEA;;GAEC,GACD,MAAM,gBAAe,EAAU;QAC7B,OAAO,WAAW,CAAC,UAAU,EAAE,IAAI;IACrC;IAEA;;GAEC,GACD,MAAM,eAAc,EAAU,EAAE,UAI/B;QACC,OAAO,WAAW,CAAC,UAAU,EAAE,IAAI,EAAE;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,aAAY,SAAiB,EAAE,MAAc,EAAE,MAAc;QACjE,OAAO,WAAW,2BAA2B;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAc,SAAiB,EAAE,YAAoB,EAAE,YAAoB,EAAE,eAAuB,MAAM;QAC9G,OAAO,WAAW,oBAAoB;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;gBACA;YACF;QACF;IACF;AACF;AAGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,iBAAgB,SAAiB;QACrC,OAAO,WAAW,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;IACpD;IAEA;;GAEC,GACD,MAAM,mBAAkB,SAAiB;QACvC,OAAO,WAAW,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC,EAAE;YAClD,QAAQ;QACV;IACF;AACF;;AAQO,MAAM,eAAe;IAC1B;;GAEC,GACD,gBAAe,MAAc;QAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA;;GAEC,GACD,oBAAmB,QAAgB,EAAE,iBAAyB,CAAC,EAAE,YAAoB,CAAC;QACpF,OAAO,WAAW,YAAY;IAChC;IAEA;;GAEC,GACD,sBAAqB,MAAc;QACjC,MAAM,UAAkC;YACtC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,SAAS;YACT,aAAa;QACf;QACA,OAAO,OAAO,CAAC,OAAO,IAAI;IAC5B;IAEA;;GAEC,GACD,mBAAkB,MAAc;QAC9B,MAAM,WAAmC;YACvC,OAAO;YACP,MAAM;YACN,WAAW;YACX,MAAM;YACN,WAAW;QACb;QACA,OAAO,QAAQ,CAAC,OAAO,IAAI;IAC7B;IAEA;;GAEC,GACD,sBAAqB,MAAc;QACjC,MAAM,WAAmC;YACvC,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;QACZ;QACA,OAAO,QAAQ,CAAC,OAAO,IAAI;IAC7B;IAEA;;GAEC,GACD,sBAAqB,MAAc;QACjC,MAAM,WAAmC;YACvC,QAAQ;YACR,MAAM;YACN,UAAU;YACV,SAAS;QACX;QACA,OAAO,QAAQ,CAAC,OAAO,IAAI;IAC7B;IAEA;;GAEC,GACD,uBAAsB,MAAc,EAAE,WAAmB;QACvD,OAAO,SAAS,KAAK,UAAU;IACjC;AACF", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/billing-notifications.ts"], "sourcesContent": ["// Comprehensive toast notification utilities for billing actions\n// Provides consistent messaging in Chinese for all billing operations\n\nimport { toast } from 'sonner';\nimport { Bill, Payment, BillItem } from '@/types/clinic';\nimport { billingUtils } from './api/billing';\n\nexport const billingNotifications = {\n  // Bill-related notifications\n  bill: {\n    created: (bill: Bill) => {\n      toast.success(`账单创建成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    updated: (bill: Bill) => {\n      toast.success(`账单更新成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    deleted: (billNumber: string) => {\n      toast.success(`账单删除成功！`, {\n        description: `账单编号: ${billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    statusUpdated: (bill: Bill, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        draft: '草稿',\n        sent: '已发送',\n        confirmed: '已确认',\n        paid: '已支付',\n        cancelled: '已取消',\n      };\n      \n      toast.success(`账单状态已更新！`, {\n        description: `${bill.billNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 5000,\n      });\n    },\n\n    generateFromAppointment: (bill: Bill, appointmentDate: string) => {\n      toast.success(`从预约生成账单成功！`, {\n        description: `预约日期: ${appointmentDate}，账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    loadError: (error?: string) => {\n      toast.error(`加载账单失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    createError: (error?: string) => {\n      toast.error(`创建账单失败`, {\n        description: error || '请检查输入信息后重试',\n        duration: 5000,\n      });\n    },\n\n    updateError: (error?: string) => {\n      toast.error(`更新账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    deleteError: (error?: string) => {\n      toast.error(`删除账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`账单验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Payment-related notifications\n  payment: {\n    processed: (payment: Payment) => {\n      toast.success(`支付处理成功！`, {\n        description: `支付金额: ${billingUtils.formatCurrency(payment.amount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    receiptGenerated: (payment: Payment) => {\n      toast.success(`收据生成成功！`, {\n        description: `收据编号: ${payment.receiptNumber || '待生成'}`,\n        duration: 4000,\n      });\n    },\n\n    refunded: (payment: Payment, refundAmount: number) => {\n      toast.success(`退款处理成功！`, {\n        description: `退款金额: ${billingUtils.formatCurrency(refundAmount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    statusUpdated: (payment: Payment, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        pending: '待处理',\n        completed: '已完成',\n        failed: '失败',\n        refunded: '已退款',\n      };\n\n      toast.success(`支付状态已更新！`, {\n        description: `${payment.paymentNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 4000,\n      });\n    },\n\n    processError: (error?: string) => {\n      toast.error(`支付处理失败`, {\n        description: error || '请检查支付信息后重试',\n        duration: 5000,\n      });\n    },\n\n    refundError: (error?: string) => {\n      toast.error(`退款处理失败`, {\n        description: error || '请联系管理员处理',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`支付验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n\n    amountExceeded: (maxAmount: number) => {\n      toast.error(`支付金额超限`, {\n        description: `最大支付金额: ${billingUtils.formatCurrency(maxAmount)}`,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Receipt-related notifications\n  receipt: {\n    printed: (receiptNumber: string) => {\n      toast.success(`收据打印成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    downloaded: (receiptNumber: string) => {\n      toast.success(`收据下载成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    printError: () => {\n      toast.error(`收据打印失败`, {\n        description: '请检查打印机设置',\n        duration: 4000,\n      });\n    },\n\n    downloadError: () => {\n      toast.error(`收据下载失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    notFound: (receiptNumber: string) => {\n      toast.error(`收据未找到`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // General system notifications\n  system: {\n    loading: (action: string) => {\n      toast.loading(`${action}中...`, {\n        duration: Infinity, // Will be dismissed manually\n      });\n    },\n\n    networkError: () => {\n      toast.error(`网络连接失败`, {\n        description: '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    permissionDenied: (action: string) => {\n      toast.error(`权限不足`, {\n        description: `您没有权限执行: ${action}`,\n        duration: 5000,\n      });\n    },\n\n    dataRefreshed: () => {\n      toast.success(`数据刷新成功`, {\n        duration: 2000,\n      });\n    },\n\n    dataRefreshError: () => {\n      toast.error(`数据刷新失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    operationCancelled: (operation: string) => {\n      toast.info(`${operation}已取消`, {\n        duration: 2000,\n      });\n    },\n\n    featureNotImplemented: (feature: string) => {\n      toast.info(`${feature}功能开发中...`, {\n        description: '敬请期待',\n        duration: 3000,\n      });\n    },\n  },\n\n  // Financial reporting notifications\n  financial: {\n    reportGenerated: (reportType: string, period: string) => {\n      toast.success(`${reportType}生成成功！`, {\n        description: `报表期间: ${period}`,\n        duration: 4000,\n      });\n    },\n\n    reportError: (reportType: string, error?: string) => {\n      toast.error(`${reportType}生成失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    dataExported: (format: string) => {\n      toast.success(`数据导出成功！`, {\n        description: `格式: ${format}`,\n        duration: 3000,\n      });\n    },\n\n    exportError: (error?: string) => {\n      toast.error(`数据导出失败`, {\n        description: error || '请稍后重试',\n        duration: 4000,\n      });\n    },\n  },\n\n  // Validation and warning notifications\n  validation: {\n    requiredField: (fieldName: string) => {\n      toast.error(`字段验证失败`, {\n        description: `${fieldName}为必填项`,\n        duration: 4000,\n      });\n    },\n\n    invalidFormat: (fieldName: string, expectedFormat: string) => {\n      toast.error(`格式验证失败`, {\n        description: `${fieldName}格式应为: ${expectedFormat}`,\n        duration: 4000,\n      });\n    },\n\n    duplicateEntry: (itemType: string, identifier: string) => {\n      toast.error(`重复条目`, {\n        description: `${itemType} \"${identifier}\" 已存在`,\n        duration: 4000,\n      });\n    },\n\n    unsavedChanges: () => {\n      toast.warning(`有未保存的更改`, {\n        description: '请保存后再继续',\n        duration: 4000,\n      });\n    },\n\n    confirmAction: (action: string) => {\n      toast.warning(`请确认操作`, {\n        description: `即将执行: ${action}`,\n        duration: 5000,\n      });\n    },\n  },\n};\n\n// Utility function to dismiss all toasts\nexport const dismissAllToasts = () => {\n  toast.dismiss();\n};\n\n// Utility function to show custom toast with consistent styling\nexport const showCustomToast = (\n  type: 'success' | 'error' | 'warning' | 'info',\n  title: string,\n  description?: string,\n  duration: number = 4000\n) => {\n  const toastFunction = toast[type];\n  toastFunction(title, {\n    description,\n    duration,\n  });\n};\n"], "names": [], "mappings": "AAAA,iEAAiE;AACjE,sEAAsE;;;;;;AAEtE;AAEA;;;AAEO,MAAM,uBAAuB;IAClC,6BAA6B;IAC7B,MAAM;QACJ,SAAS,CAAC;YACR,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,KAAK,UAAU,EAAE;gBACvC,UAAU;YACZ;QACF;QAEA,SAAS,CAAC;YACR,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,KAAK,UAAU,EAAE;gBACvC,UAAU;YACZ;QACF;QAEA,SAAS,CAAC;YACR,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,YAAY;gBAClC,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,MAAY,WAAmB;YAC7C,MAAM,cAAc;gBAClB,OAAO;gBACP,MAAM;gBACN,WAAW;gBACX,MAAM;gBACN,WAAW;YACb;YAEA,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACxB,aAAa,GAAG,KAAK,UAAU,CAAC,EAAE,EAAE,WAAW,CAAC,UAAsC,CAAC,GAAG,EAAE,WAAW,CAAC,UAAsC,EAAE;gBAChJ,UAAU;YACZ;QACF;QAEA,yBAAyB,CAAC,MAAY;YACpC,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,EAAE;gBAC1B,aAAa,CAAC,MAAM,EAAE,gBAAgB,OAAO,EAAE,KAAK,UAAU,EAAE;gBAChE,UAAU;YACZ;QACF;QAEA,WAAW,CAAC;YACV,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,iBAAiB,CAAC;YAChB,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;IACF;IAEA,gCAAgC;IAChC,SAAS;QACP,WAAW,CAAC;YACV,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,8HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,MAAM,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE;gBAClG,UAAU;YACZ;QACF;QAEA,kBAAkB,CAAC;YACjB,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,QAAQ,aAAa,IAAI,OAAO;gBACtD,UAAU;YACZ;QACF;QAEA,UAAU,CAAC,SAAkB;YAC3B,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,8HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,cAAc,OAAO,EAAE,QAAQ,aAAa,EAAE;gBAChG,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,SAAkB,WAAmB;YACnD,MAAM,cAAc;gBAClB,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;YACZ;YAEA,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACxB,aAAa,GAAG,QAAQ,aAAa,CAAC,EAAE,EAAE,WAAW,CAAC,UAAsC,CAAC,GAAG,EAAE,WAAW,CAAC,UAAsC,EAAE;gBACtJ,UAAU;YACZ;QACF;QAEA,cAAc,CAAC;YACb,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,iBAAiB,CAAC;YAChB,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,gBAAgB,CAAC;YACf,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,CAAC,QAAQ,EAAE,8HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,YAAY;gBAChE,UAAU;YACZ;QACF;IACF;IAEA,gCAAgC;IAChC,SAAS;QACP,SAAS,CAAC;YACR,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,eAAe;gBACrC,UAAU;YACZ;QACF;QAEA,YAAY,CAAC;YACX,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,eAAe;gBACrC,UAAU;YACZ;QACF;QAEA,YAAY;YACV,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,eAAe;YACb,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,UAAU,CAAC;YACT,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;gBACnB,aAAa,CAAC,MAAM,EAAE,eAAe;gBACrC,UAAU;YACZ;QACF;IACF;IAEA,+BAA+B;IAC/B,QAAQ;QACN,SAAS,CAAC;YACR,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,EAAE;gBAC7B,UAAU;YACZ;QACF;QAEA,cAAc;YACZ,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,kBAAkB,CAAC;YACjB,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClB,aAAa,CAAC,SAAS,EAAE,QAAQ;gBACjC,UAAU;YACZ;QACF;QAEA,eAAe;YACb,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE;gBACtB,UAAU;YACZ;QACF;QAEA,kBAAkB;YAChB,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,oBAAoB,CAAC;YACnB,0QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE;gBAC5B,UAAU;YACZ;QACF;QAEA,uBAAuB,CAAC;YACtB,0QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,QAAQ,CAAC,EAAE;gBAC/B,aAAa;gBACb,UAAU;YACZ;QACF;IACF;IAEA,oCAAoC;IACpC,WAAW;QACT,iBAAiB,CAAC,YAAoB;YACpC,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,WAAW,KAAK,CAAC,EAAE;gBAClC,aAAa,CAAC,MAAM,EAAE,QAAQ;gBAC9B,UAAU;YACZ;QACF;QAEA,aAAa,CAAC,YAAoB;YAChC,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,EAAE;gBAC/B,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,cAAc,CAAC;YACb,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,IAAI,EAAE,QAAQ;gBAC5B,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;IACF;IAEA,uCAAuC;IACvC,YAAY;QACV,eAAe,CAAC;YACd,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,GAAG,UAAU,IAAI,CAAC;gBAC/B,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,WAAmB;YACjC,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,GAAG,UAAU,MAAM,EAAE,gBAAgB;gBAClD,UAAU;YACZ;QACF;QAEA,gBAAgB,CAAC,UAAkB;YACjC,0QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClB,aAAa,GAAG,SAAS,EAAE,EAAE,WAAW,KAAK,CAAC;gBAC9C,UAAU;YACZ;QACF;QAEA,gBAAgB;YACd,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,eAAe,CAAC;YACd,0QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;gBACrB,aAAa,CAAC,MAAM,EAAE,QAAQ;gBAC9B,UAAU;YACZ;QACF;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,0QAAA,CAAA,QAAK,CAAC,OAAO;AACf;AAGO,MAAM,kBAAkB,CAC7B,MACA,OACA,aACA,WAAmB,IAAI;IAEvB,MAAM,gBAAgB,0QAAA,CAAA,QAAK,CAAC,KAAK;IACjC,cAAc,OAAO;QACnB;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/validation/validation-utils.ts"], "sourcesContent": ["// Validation utilities for real-time form validation and error handling\n// Provides consistent validation feedback across all billing forms\n\nimport { z } from 'zod';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Validation result interface\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: ValidationError[];\n  warnings: ValidationWarning[];\n}\n\nexport interface ValidationError {\n  field: string;\n  message: string;\n  code: string;\n  severity: 'error' | 'warning';\n}\n\nexport interface ValidationWarning {\n  field: string;\n  message: string;\n  suggestion?: string;\n}\n\n// Real-time validation debounce utility\nexport class ValidationDebouncer {\n  private timeouts: Map<string, NodeJS.Timeout> = new Map();\n  private readonly delay: number;\n\n  constructor(delay: number = 500) {\n    this.delay = delay;\n  }\n\n  debounce<T extends any[]>(\n    key: string,\n    callback: (...args: T) => void,\n    ...args: T\n  ): void {\n    // Clear existing timeout for this key\n    const existingTimeout = this.timeouts.get(key);\n    if (existingTimeout) {\n      clearTimeout(existingTimeout);\n    }\n\n    // Set new timeout\n    const timeout = setTimeout(() => {\n      callback(...args);\n      this.timeouts.delete(key);\n    }, this.delay);\n\n    this.timeouts.set(key, timeout);\n  }\n\n  clear(key?: string): void {\n    if (key) {\n      const timeout = this.timeouts.get(key);\n      if (timeout) {\n        clearTimeout(timeout);\n        this.timeouts.delete(key);\n      }\n    } else {\n      // Clear all timeouts\n      this.timeouts.forEach(timeout => clearTimeout(timeout));\n      this.timeouts.clear();\n    }\n  }\n}\n\n// Field-level validation utility\nexport class FieldValidator {\n  private schema: z.ZodSchema;\n  private debouncer: ValidationDebouncer;\n\n  constructor(schema: z.ZodSchema, debounceDelay: number = 300) {\n    this.schema = schema;\n    this.debouncer = new ValidationDebouncer(debounceDelay);\n  }\n\n  validateField(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    this.debouncer.debounce(\n      fieldPath,\n      this.performFieldValidation.bind(this),\n      fieldPath,\n      value,\n      fullData,\n      onValidation\n    );\n  }\n\n  private performFieldValidation(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    try {\n      // Create a partial object with just this field\n      const fieldData = this.setNestedValue({}, fieldPath, value);\n      \n      // Merge with existing data\n      const testData = { ...fullData, ...fieldData };\n      \n      // Validate the full object but focus on this field\n      const result = this.schema.safeParse(testData);\n      \n      const fieldErrors = result.success \n        ? []\n        : result.error.errors\n            .filter(error => error.path.join('.') === fieldPath)\n            .map(error => ({\n              field: fieldPath,\n              message: error.message,\n              code: error.code,\n              severity: 'error' as const,\n            }));\n\n      const warnings = this.generateWarnings(fieldPath, value, fullData);\n\n      const validationResult: ValidationResult = {\n        isValid: fieldErrors.length === 0,\n        errors: fieldErrors,\n        warnings,\n      };\n\n      if (onValidation) {\n        onValidation(validationResult);\n      }\n    } catch (error) {\n      console.error('Field validation error:', error);\n      if (onValidation) {\n        onValidation({\n          isValid: false,\n          errors: [{\n            field: fieldPath,\n            message: '验证过程中发生错误',\n            code: 'VALIDATION_ERROR',\n            severity: 'error',\n          }],\n          warnings: [],\n        });\n      }\n    }\n  }\n\n  private setNestedValue(obj: any, path: string, value: any): any {\n    const keys = path.split('.');\n    let current = obj;\n    \n    for (let i = 0; i < keys.length - 1; i++) {\n      const key = keys[i];\n      if (!(key in current)) {\n        current[key] = {};\n      }\n      current = current[key];\n    }\n    \n    current[keys[keys.length - 1]] = value;\n    return obj;\n  }\n\n  private generateWarnings(fieldPath: string, value: any, fullData: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate context-specific warnings\n    switch (fieldPath) {\n      case 'amount':\n        if (typeof value === 'number' && value > 10000) {\n          warnings.push({\n            field: fieldPath,\n            message: '金额较大，请确认是否正确',\n            suggestion: '检查金额是否输入正确',\n          });\n        }\n        break;\n\n      case 'dueDate':\n        if (value) {\n          const dueDate = new Date(value);\n          const today = new Date();\n          const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n          \n          if (daysDiff > 365) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期距离现在超过一年',\n              suggestion: '考虑设置更近的到期日期',\n            });\n          } else if (daysDiff < 7) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期较近',\n              suggestion: '确保有足够时间处理账单',\n            });\n          }\n        }\n        break;\n\n      case 'discountAmount':\n        if (typeof value === 'number' && value > 0 && fullData.items) {\n          const subtotal = fullData.items.reduce((sum: number, item: any) => {\n            return sum + (item.quantity || 0) * (item.unitPrice || 0);\n          }, 0);\n          \n          if (value > subtotal * 0.5) {\n            warnings.push({\n              field: fieldPath,\n              message: '折扣金额超过小计的50%',\n              suggestion: '确认折扣金额是否正确',\n            });\n          }\n        }\n        break;\n\n      case 'unitPrice':\n        if (typeof value === 'number' && value === 0) {\n          warnings.push({\n            field: fieldPath,\n            message: '单价为0，确认是否为免费项目',\n            suggestion: '如果不是免费项目，请输入正确单价',\n          });\n        }\n        break;\n    }\n\n    return warnings;\n  }\n\n  cleanup(): void {\n    this.debouncer.clear();\n  }\n}\n\n// Form-level validation utility\nexport class FormValidator {\n  private schema: z.ZodSchema;\n  private fieldValidators: Map<string, FieldValidator> = new Map();\n\n  constructor(schema: z.ZodSchema) {\n    this.schema = schema;\n  }\n\n  validateForm(data: any): ValidationResult {\n    try {\n      const result = this.schema.safeParse(data);\n      \n      if (result.success) {\n        return {\n          isValid: true,\n          errors: [],\n          warnings: this.generateFormWarnings(data),\n        };\n      }\n\n      const errors = result.error.errors.map(error => ({\n        field: error.path.join('.'),\n        message: error.message,\n        code: error.code,\n        severity: 'error' as const,\n      }));\n\n      return {\n        isValid: false,\n        errors,\n        warnings: this.generateFormWarnings(data),\n      };\n    } catch (error) {\n      console.error('Form validation error:', error);\n      return {\n        isValid: false,\n        errors: [{\n          field: 'form',\n          message: '表单验证过程中发生错误',\n          code: 'FORM_VALIDATION_ERROR',\n          severity: 'error',\n        }],\n        warnings: [],\n      };\n    }\n  }\n\n  private generateFormWarnings(data: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate form-level warnings\n    if (data.items && Array.isArray(data.items)) {\n      const totalItems = data.items.length;\n      if (totalItems > 20) {\n        warnings.push({\n          field: 'items',\n          message: `账单包含${totalItems}个项目，较多`,\n          suggestion: '考虑合并相似项目或分拆为多个账单',\n        });\n      }\n\n      const totalAmount = data.items.reduce((sum: number, item: any) => {\n        return sum + (item.quantity || 0) * (item.unitPrice || 0);\n      }, 0);\n\n      if (totalAmount > 50000) {\n        warnings.push({\n          field: 'form',\n          message: '账单总金额较大',\n          suggestion: '确认金额计算是否正确',\n        });\n      }\n    }\n\n    return warnings;\n  }\n\n  getFieldValidator(fieldPath: string): FieldValidator {\n    if (!this.fieldValidators.has(fieldPath)) {\n      this.fieldValidators.set(fieldPath, new FieldValidator(this.schema));\n    }\n    return this.fieldValidators.get(fieldPath)!;\n  }\n\n  cleanup(): void {\n    this.fieldValidators.forEach(validator => validator.cleanup());\n    this.fieldValidators.clear();\n  }\n}\n\n// Validation error display utility\nexport const displayValidationErrors = (errors: ValidationError[]): void => {\n  errors.forEach(error => {\n    if (error.severity === 'error') {\n      billingNotifications.validation.requiredField(error.field);\n    }\n  });\n};\n\n// Business logic validation\nexport const validateBusinessRules = {\n  // Check if bill can be marked as paid\n  canMarkAsPaid: (bill: any): { valid: boolean; reason?: string } => {\n    if ((bill.remainingAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '账单还有未支付金额，无法标记为已支付',\n      };\n    }\n    return { valid: true };\n  },\n\n  // Check if payment amount is valid for bill\n  isValidPaymentAmount: (amount: number, bill: any): { valid: boolean; reason?: string } => {\n    const remainingAmount = bill.remainingAmount || 0;\n    \n    if (amount > remainingAmount) {\n      return {\n        valid: false,\n        reason: `支付金额不能超过待付金额 $${remainingAmount.toFixed(2)}`,\n      };\n    }\n    \n    if (amount <= 0) {\n      return {\n        valid: false,\n        reason: '支付金额必须大于0',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill can be deleted\n  canDeleteBill: (bill: any): { valid: boolean; reason?: string } => {\n    if (bill.status === 'paid') {\n      return {\n        valid: false,\n        reason: '已支付的账单不能删除',\n      };\n    }\n    \n    if ((bill.paidAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '已有支付记录的账单不能删除',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill status transition is valid\n  isValidStatusTransition: (fromStatus: string, toStatus: string): { valid: boolean; reason?: string } => {\n    const validTransitions: Record<string, string[]> = {\n      draft: ['sent', 'cancelled'],\n      sent: ['confirmed', 'cancelled'],\n      confirmed: ['paid', 'cancelled'],\n      paid: [], // No transitions from paid\n      cancelled: [], // No transitions from cancelled\n    };\n\n    const allowedTransitions = validTransitions[fromStatus] || [];\n    \n    if (!allowedTransitions.includes(toStatus)) {\n      return {\n        valid: false,\n        reason: `不能从\"${fromStatus}\"状态转换到\"${toStatus}\"状态`,\n      };\n    }\n    \n    return { valid: true };\n  },\n};\n\n// Export validation constants\nexport const VALIDATION_CONSTANTS = {\n  MAX_BILL_AMOUNT: 999999.99,\n  MAX_ITEMS_PER_BILL: 50,\n  MAX_DESCRIPTION_LENGTH: 200,\n  MAX_NOTES_LENGTH: 1000,\n  MIN_PAYMENT_AMOUNT: 0.01,\n  MAX_DISCOUNT_RATE: 100,\n  DEBOUNCE_DELAY: 300,\n} as const;\n"], "names": [], "mappings": "AAAA,wEAAwE;AACxE,mEAAmE;;;;;;;;;AAGnE;;AAuBO,MAAM;IACH,WAAwC,IAAI,MAAM;IACzC,MAAc;IAE/B,YAAY,QAAgB,GAAG,CAAE;QAC/B,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,SACE,GAAW,EACX,QAA8B,EAC9B,GAAG,IAAO,EACJ;QACN,sCAAsC;QACtC,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAC1C,IAAI,iBAAiB;YACnB,aAAa;QACf;QAEA,kBAAkB;QAClB,MAAM,UAAU,WAAW;YACzB,YAAY;YACZ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACvB,GAAG,IAAI,CAAC,KAAK;QAEb,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK;IACzB;IAEA,MAAM,GAAY,EAAQ;QACxB,IAAI,KAAK;YACP,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS;gBACX,aAAa;gBACb,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvB;QACF,OAAO;YACL,qBAAqB;YACrB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA,UAAW,aAAa;YAC9C,IAAI,CAAC,QAAQ,CAAC,KAAK;QACrB;IACF;AACF;AAGO,MAAM;IACH,OAAoB;IACpB,UAA+B;IAEvC,YAAY,MAAmB,EAAE,gBAAwB,GAAG,CAAE;QAC5D,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB;IAC3C;IAEA,cACE,SAAiB,EACjB,KAAU,EACV,QAAa,EACb,YAAiD,EAC3C;QACN,IAAI,CAAC,SAAS,CAAC,QAAQ,CACrB,WACA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,GACrC,WACA,OACA,UACA;IAEJ;IAEQ,uBACN,SAAiB,EACjB,KAAU,EACV,QAAa,EACb,YAAiD,EAC3C;QACN,IAAI;YACF,+CAA+C;YAC/C,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,WAAW;YAErD,2BAA2B;YAC3B,MAAM,WAAW;gBAAE,GAAG,QAAQ;gBAAE,GAAG,SAAS;YAAC;YAE7C,mDAAmD;YACnD,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAErC,MAAM,cAAc,OAAO,OAAO,GAC9B,EAAE,GACF,OAAO,KAAK,CAAC,MAAM,CAChB,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,WACzC,GAAG,CAAC,CAAA,QAAS,CAAC;oBACb,OAAO;oBACP,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,UAAU;gBACZ,CAAC;YAEP,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,WAAW,OAAO;YAEzD,MAAM,mBAAqC;gBACzC,SAAS,YAAY,MAAM,KAAK;gBAChC,QAAQ;gBACR;YACF;YAEA,IAAI,cAAc;gBAChB,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,cAAc;gBAChB,aAAa;oBACX,SAAS;oBACT,QAAQ;wBAAC;4BACP,OAAO;4BACP,SAAS;4BACT,MAAM;4BACN,UAAU;wBACZ;qBAAE;oBACF,UAAU,EAAE;gBACd;YACF;QACF;IACF;IAEQ,eAAe,GAAQ,EAAE,IAAY,EAAE,KAAU,EAAO;QAC9D,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,IAAI,UAAU;QAEd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;YACxC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,CAAC,OAAO,OAAO,GAAG;gBACrB,OAAO,CAAC,IAAI,GAAG,CAAC;YAClB;YACA,UAAU,OAAO,CAAC,IAAI;QACxB;QAEA,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG;QACjC,OAAO;IACT;IAEQ,iBAAiB,SAAiB,EAAE,KAAU,EAAE,QAAa,EAAuB;QAC1F,MAAM,WAAgC,EAAE;QAExC,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,IAAI,OAAO,UAAU,YAAY,QAAQ,OAAO;oBAC9C,SAAS,IAAI,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,YAAY;oBACd;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO;oBACT,MAAM,UAAU,IAAI,KAAK;oBACzB,MAAM,QAAQ,IAAI;oBAClB,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;oBAEvF,IAAI,WAAW,KAAK;wBAClB,SAAS,IAAI,CAAC;4BACZ,OAAO;4BACP,SAAS;4BACT,YAAY;wBACd;oBACF,OAAO,IAAI,WAAW,GAAG;wBACvB,SAAS,IAAI,CAAC;4BACZ,OAAO;4BACP,SAAS;4BACT,YAAY;wBACd;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO,UAAU,YAAY,QAAQ,KAAK,SAAS,KAAK,EAAE;oBAC5D,MAAM,WAAW,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,KAAa;wBACnD,OAAO,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC;oBAC1D,GAAG;oBAEH,IAAI,QAAQ,WAAW,KAAK;wBAC1B,SAAS,IAAI,CAAC;4BACZ,OAAO;4BACP,SAAS;4BACT,YAAY;wBACd;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO,UAAU,YAAY,UAAU,GAAG;oBAC5C,SAAS,IAAI,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,YAAY;oBACd;gBACF;gBACA;QACJ;QAEA,OAAO;IACT;IAEA,UAAgB;QACd,IAAI,CAAC,SAAS,CAAC,KAAK;IACtB;AACF;AAGO,MAAM;IACH,OAAoB;IACpB,kBAA+C,IAAI,MAAM;IAEjE,YAAY,MAAmB,CAAE;QAC/B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,aAAa,IAAS,EAAoB;QACxC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAErC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO;oBACL,SAAS;oBACT,QAAQ,EAAE;oBACV,UAAU,IAAI,CAAC,oBAAoB,CAAC;gBACtC;YACF;YAEA,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC/C,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC;oBACvB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,UAAU;gBACZ,CAAC;YAED,OAAO;gBACL,SAAS;gBACT;gBACA,UAAU,IAAI,CAAC,oBAAoB,CAAC;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAC;wBACP,OAAO;wBACP,SAAS;wBACT,MAAM;wBACN,UAAU;oBACZ;iBAAE;gBACF,UAAU,EAAE;YACd;QACF;IACF;IAEQ,qBAAqB,IAAS,EAAuB;QAC3D,MAAM,WAAgC,EAAE;QAExC,+BAA+B;QAC/B,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,GAAG;YAC3C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM;YACpC,IAAI,aAAa,IAAI;gBACnB,SAAS,IAAI,CAAC;oBACZ,OAAO;oBACP,SAAS,CAAC,IAAI,EAAE,WAAW,MAAM,CAAC;oBAClC,YAAY;gBACd;YACF;YAEA,MAAM,cAAc,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAa;gBAClD,OAAO,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC;YAC1D,GAAG;YAEH,IAAI,cAAc,OAAO;gBACvB,SAAS,IAAI,CAAC;oBACZ,OAAO;oBACP,SAAS;oBACT,YAAY;gBACd;YACF;QACF;QAEA,OAAO;IACT;IAEA,kBAAkB,SAAiB,EAAkB;QACnD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY;YACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,IAAI,eAAe,IAAI,CAAC,MAAM;QACpE;QACA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;IAEA,UAAgB;QACd,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA,YAAa,UAAU,OAAO;QAC3D,IAAI,CAAC,eAAe,CAAC,KAAK;IAC5B;AACF;AAGO,MAAM,0BAA0B,CAAC;IACtC,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,MAAM,QAAQ,KAAK,SAAS;YAC9B,wIAAA,CAAA,uBAAoB,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,KAAK;QAC3D;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,sCAAsC;IACtC,eAAe,CAAC;QACd,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,GAAG;YACnC,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QACA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,4CAA4C;IAC5C,sBAAsB,CAAC,QAAgB;QACrC,MAAM,kBAAkB,KAAK,eAAe,IAAI;QAEhD,IAAI,SAAS,iBAAiB;YAC5B,OAAO;gBACL,OAAO;gBACP,QAAQ,CAAC,cAAc,EAAE,gBAAgB,OAAO,CAAC,IAAI;YACvD;QACF;QAEA,IAAI,UAAU,GAAG;YACf,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,+BAA+B;IAC/B,eAAe,CAAC;QACd,IAAI,KAAK,MAAM,KAAK,QAAQ;YAC1B,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,IAAI,GAAG;YAC9B,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,2CAA2C;IAC3C,yBAAyB,CAAC,YAAoB;QAC5C,MAAM,mBAA6C;YACjD,OAAO;gBAAC;gBAAQ;aAAY;YAC5B,MAAM;gBAAC;gBAAa;aAAY;YAChC,WAAW;gBAAC;gBAAQ;aAAY;YAChC,MAAM,EAAE;YACR,WAAW,EAAE;QACf;QAEA,MAAM,qBAAqB,gBAAgB,CAAC,WAAW,IAAI,EAAE;QAE7D,IAAI,CAAC,mBAAmB,QAAQ,CAAC,WAAW;YAC1C,OAAO;gBACL,OAAO;gBACP,QAAQ,CAAC,IAAI,EAAE,WAAW,OAAO,EAAE,SAAS,GAAG,CAAC;YAClD;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;AACF;AAGO,MAAM,uBAAuB;IAClC,iBAAiB;IACjB,oBAAoB;IACpB,wBAAwB;IACxB,kBAAkB;IAClB,oBAAoB;IACpB,mBAAmB;IACnB,gBAAgB;AAClB", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/billing-security.ts"], "sourcesContent": ["// Comprehensive security measures for billing system\n// Handles data encryption, audit logging, and secure payment processing\n\nimport crypto from 'crypto';\n\n// Security configuration\nconst SECURITY_CONFIG = {\n  encryption: {\n    algorithm: 'aes-256-gcm',\n    keyLength: 32,\n    ivLength: 16,\n    tagLength: 16,\n  },\n  audit: {\n    maxLogSize: 10000, // Maximum number of audit log entries\n    sensitiveFields: ['cardNumber', 'cvv', 'bankAccount', 'transactionId'],\n  },\n  rateLimit: {\n    maxRequestsPerMinute: 60,\n    maxPaymentRequestsPerHour: 10,\n  },\n};\n\n// Encryption utilities for sensitive financial data\nexport class DataEncryption {\n  private static getEncryptionKey(): string {\n    const key = process.env.BILLING_ENCRYPTION_KEY;\n    if (!key) {\n      throw new Error('BILLING_ENCRYPTION_KEY environment variable is required');\n    }\n    return key;\n  }\n\n  /**\n   * Encrypt sensitive data\n   */\n  static encrypt(data: string): { encrypted: string; iv: string; tag: string } {\n    try {\n      const key = Buffer.from(this.getEncryptionKey(), 'hex');\n      const iv = crypto.randomBytes(SECURITY_CONFIG.encryption.ivLength);\n      const cipher = crypto.createCipher(SECURITY_CONFIG.encryption.algorithm, key);\n      cipher.setAAD(Buffer.from('billing-data'));\n\n      let encrypted = cipher.update(data, 'utf8', 'hex');\n      encrypted += cipher.final('hex');\n      \n      const tag = cipher.getAuthTag();\n\n      return {\n        encrypted,\n        iv: iv.toString('hex'),\n        tag: tag.toString('hex'),\n      };\n    } catch (error) {\n      console.error('Encryption error:', error);\n      throw new Error('Failed to encrypt sensitive data');\n    }\n  }\n\n  /**\n   * Decrypt sensitive data\n   */\n  static decrypt(encryptedData: { encrypted: string; iv: string; tag: string }): string {\n    try {\n      const key = Buffer.from(this.getEncryptionKey(), 'hex');\n      const iv = Buffer.from(encryptedData.iv, 'hex');\n      const tag = Buffer.from(encryptedData.tag, 'hex');\n      \n      const decipher = crypto.createDecipher(SECURITY_CONFIG.encryption.algorithm, key);\n      decipher.setAAD(Buffer.from('billing-data'));\n      decipher.setAuthTag(tag);\n\n      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');\n      decrypted += decipher.final('utf8');\n\n      return decrypted;\n    } catch (error) {\n      console.error('Decryption error:', error);\n      throw new Error('Failed to decrypt sensitive data');\n    }\n  }\n\n  /**\n   * Hash sensitive data for comparison (one-way)\n   */\n  static hash(data: string): string {\n    return crypto.createHash('sha256').update(data).digest('hex');\n  }\n\n  /**\n   * Generate secure random token\n   */\n  static generateSecureToken(length: number = 32): string {\n    return crypto.randomBytes(length).toString('hex');\n  }\n}\n\n// Audit logging for financial operations\nexport interface AuditLogEntry {\n  id: string;\n  timestamp: Date;\n  userId: string;\n  userEmail: string;\n  action: string;\n  resource: string;\n  resourceId?: string;\n  details: any;\n  ipAddress?: string;\n  userAgent?: string;\n  success: boolean;\n  errorMessage?: string;\n}\n\nexport class AuditLogger {\n  private static instance: AuditLogger;\n  private auditLog: AuditLogEntry[] = [];\n\n  private constructor() {}\n\n  static getInstance(): AuditLogger {\n    if (!AuditLogger.instance) {\n      AuditLogger.instance = new AuditLogger();\n    }\n    return AuditLogger.instance;\n  }\n\n  /**\n   * Log financial operation\n   */\n  logFinancialOperation(\n    userId: string,\n    userEmail: string,\n    action: string,\n    resource: string,\n    details: any,\n    success: boolean = true,\n    errorMessage?: string,\n    request?: Request\n  ): void {\n    const entry: AuditLogEntry = {\n      id: DataEncryption.generateSecureToken(16),\n      timestamp: new Date(),\n      userId,\n      userEmail,\n      action,\n      resource,\n      resourceId: details.id || details.billId || details.paymentId,\n      details: this.sanitizeDetails(details),\n      ipAddress: this.getClientIP(request),\n      userAgent: request?.headers.get('user-agent') || undefined,\n      success,\n      errorMessage,\n    };\n\n    this.auditLog.push(entry);\n\n    // Keep only the most recent entries\n    if (this.auditLog.length > SECURITY_CONFIG.audit.maxLogSize) {\n      this.auditLog.shift();\n    }\n\n    // Log to console for development (in production, this should go to a secure logging service)\n    console.log(`[AUDIT] ${entry.timestamp.toISOString()} - ${entry.action} on ${entry.resource} by ${entry.userEmail} - ${entry.success ? 'SUCCESS' : 'FAILED'}`);\n  }\n\n  /**\n   * Get audit log entries (filtered for security)\n   */\n  getAuditLog(userId?: string, limit: number = 100): AuditLogEntry[] {\n    let filteredLog = this.auditLog;\n\n    if (userId) {\n      filteredLog = filteredLog.filter(entry => entry.userId === userId);\n    }\n\n    return filteredLog\n      .slice(-limit)\n      .map(entry => ({\n        ...entry,\n        details: this.sanitizeDetails(entry.details),\n      }));\n  }\n\n  /**\n   * Remove sensitive information from audit details\n   */\n  private sanitizeDetails(details: any): any {\n    if (!details || typeof details !== 'object') {\n      return details;\n    }\n\n    const sanitized = { ...details };\n    \n    SECURITY_CONFIG.audit.sensitiveFields.forEach(field => {\n      if (sanitized[field]) {\n        sanitized[field] = this.maskSensitiveData(sanitized[field]);\n      }\n    });\n\n    return sanitized;\n  }\n\n  /**\n   * Mask sensitive data for logging\n   */\n  private maskSensitiveData(data: string): string {\n    if (data.length <= 4) {\n      return '****';\n    }\n    return data.substring(0, 2) + '*'.repeat(data.length - 4) + data.substring(data.length - 2);\n  }\n\n  /**\n   * Extract client IP address from request\n   */\n  private getClientIP(request?: Request): string | undefined {\n    if (!request) return undefined;\n\n    const forwarded = request.headers.get('x-forwarded-for');\n    if (forwarded) {\n      return forwarded.split(',')[0].trim();\n    }\n\n    const realIP = request.headers.get('x-real-ip');\n    if (realIP) {\n      return realIP;\n    }\n\n    return 'unknown';\n  }\n}\n\n// Rate limiting for API endpoints\nexport class RateLimiter {\n  private static instance: RateLimiter;\n  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();\n  private paymentCounts: Map<string, { count: number; resetTime: number }> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): RateLimiter {\n    if (!RateLimiter.instance) {\n      RateLimiter.instance = new RateLimiter();\n    }\n    return RateLimiter.instance;\n  }\n\n  /**\n   * Check if request is within rate limit\n   */\n  checkRateLimit(userId: string, isPaymentRequest: boolean = false): { allowed: boolean; resetTime?: number } {\n    const now = Date.now();\n    const limits = isPaymentRequest \n      ? { max: SECURITY_CONFIG.rateLimit.maxPaymentRequestsPerHour, window: 60 * 60 * 1000 }\n      : { max: SECURITY_CONFIG.rateLimit.maxRequestsPerMinute, window: 60 * 1000 };\n\n    const counts = isPaymentRequest ? this.paymentCounts : this.requestCounts;\n    const userCount = counts.get(userId);\n\n    if (!userCount || now > userCount.resetTime) {\n      // Reset or initialize counter\n      counts.set(userId, { count: 1, resetTime: now + limits.window });\n      return { allowed: true };\n    }\n\n    if (userCount.count >= limits.max) {\n      return { allowed: false, resetTime: userCount.resetTime };\n    }\n\n    userCount.count++;\n    return { allowed: true };\n  }\n\n  /**\n   * Clear expired rate limit entries\n   */\n  cleanup(): void {\n    const now = Date.now();\n    \n    for (const [userId, data] of this.requestCounts.entries()) {\n      if (now > data.resetTime) {\n        this.requestCounts.delete(userId);\n      }\n    }\n\n    for (const [userId, data] of this.paymentCounts.entries()) {\n      if (now > data.resetTime) {\n        this.paymentCounts.delete(userId);\n      }\n    }\n  }\n}\n\n// Input sanitization for financial data\nexport class InputSanitizer {\n  /**\n   * Sanitize and validate monetary amounts\n   */\n  static sanitizeAmount(amount: any): number {\n    if (typeof amount === 'number') {\n      if (!isFinite(amount) || amount < 0) {\n        throw new Error('Invalid amount: must be a positive finite number');\n      }\n      return Math.round(amount * 100) / 100; // Round to 2 decimal places\n    }\n\n    if (typeof amount === 'string') {\n      const parsed = parseFloat(amount.replace(/[^\\d.-]/g, ''));\n      if (isNaN(parsed) || parsed < 0) {\n        throw new Error('Invalid amount: must be a positive number');\n      }\n      return Math.round(parsed * 100) / 100;\n    }\n\n    throw new Error('Invalid amount: must be a number or numeric string');\n  }\n\n  /**\n   * Sanitize text input to prevent XSS and injection attacks\n   */\n  static sanitizeText(text: string, maxLength: number = 1000): string {\n    if (typeof text !== 'string') {\n      throw new Error('Input must be a string');\n    }\n\n    // Remove potentially dangerous characters and HTML tags\n    const sanitized = text\n      .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n      .replace(/<[^>]*>/g, '')\n      .replace(/javascript:/gi, '')\n      .replace(/on\\w+\\s*=/gi, '')\n      .trim();\n\n    if (sanitized.length > maxLength) {\n      throw new Error(`Input too long: maximum ${maxLength} characters allowed`);\n    }\n\n    return sanitized;\n  }\n\n  /**\n   * Validate and sanitize payment method\n   */\n  static sanitizePaymentMethod(method: string): string {\n    const validMethods = ['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'];\n    \n    if (!validMethods.includes(method)) {\n      throw new Error('Invalid payment method');\n    }\n\n    return method;\n  }\n\n  /**\n   * Sanitize transaction ID\n   */\n  static sanitizeTransactionId(transactionId: string): string {\n    if (typeof transactionId !== 'string') {\n      throw new Error('Transaction ID must be a string');\n    }\n\n    // Allow only alphanumeric characters, hyphens, and underscores\n    const sanitized = transactionId.replace(/[^a-zA-Z0-9\\-_]/g, '');\n    \n    if (sanitized.length < 3 || sanitized.length > 100) {\n      throw new Error('Transaction ID must be between 3 and 100 characters');\n    }\n\n    return sanitized;\n  }\n}\n\n// Export singleton instances\nexport const auditLogger = AuditLogger.getInstance();\nexport const rateLimiter = RateLimiter.getInstance();\n\n// Cleanup function to be called periodically\nexport const cleanupSecurity = () => {\n  rateLimiter.cleanup();\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,wEAAwE;;;;;;;;;;AAExE;;AAEA,yBAAyB;AACzB,MAAM,kBAAkB;IACtB,YAAY;QACV,WAAW;QACX,WAAW;QACX,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,YAAY;QACZ,iBAAiB;YAAC;YAAc;YAAO;YAAe;SAAgB;IACxE;IACA,WAAW;QACT,sBAAsB;QACtB,2BAA2B;IAC7B;AACF;AAGO,MAAM;IACX,OAAe,mBAA2B;QACxC,MAAM,MAAM,QAAQ,GAAG,CAAC,sBAAsB;QAC9C,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM;QAClB;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,QAAQ,IAAY,EAAkD;QAC3E,IAAI;YACF,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI;YACjD,MAAM,KAAK,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,gBAAgB,UAAU,CAAC,QAAQ;YACjE,MAAM,SAAS,qGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,gBAAgB,UAAU,CAAC,SAAS,EAAE;YACzE,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC;YAE1B,IAAI,YAAY,OAAO,MAAM,CAAC,MAAM,QAAQ;YAC5C,aAAa,OAAO,KAAK,CAAC;YAE1B,MAAM,MAAM,OAAO,UAAU;YAE7B,OAAO;gBACL;gBACA,IAAI,GAAG,QAAQ,CAAC;gBAChB,KAAK,IAAI,QAAQ,CAAC;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,OAAO,QAAQ,aAA6D,EAAU;QACpF,IAAI;YACF,MAAM,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI;YACjD,MAAM,KAAK,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE;YACzC,MAAM,MAAM,OAAO,IAAI,CAAC,cAAc,GAAG,EAAE;YAE3C,MAAM,WAAW,qGAAA,CAAA,UAAM,CAAC,cAAc,CAAC,gBAAgB,UAAU,CAAC,SAAS,EAAE;YAC7E,SAAS,MAAM,CAAC,OAAO,IAAI,CAAC;YAC5B,SAAS,UAAU,CAAC;YAEpB,IAAI,YAAY,SAAS,MAAM,CAAC,cAAc,SAAS,EAAE,OAAO;YAChE,aAAa,SAAS,KAAK,CAAC;YAE5B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,IAAY,EAAU;QAChC,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,UAAU,MAAM,CAAC,MAAM,MAAM,CAAC;IACzD;IAEA;;GAEC,GACD,OAAO,oBAAoB,SAAiB,EAAE,EAAU;QACtD,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,QAAQ,QAAQ,CAAC;IAC7C;AACF;AAkBO,MAAM;IACX,OAAe,SAAsB;IAC7B,WAA4B,EAAE,CAAC;IAEvC,aAAsB,CAAC;IAEvB,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA;;GAEC,GACD,sBACE,MAAc,EACd,SAAiB,EACjB,MAAc,EACd,QAAgB,EAChB,OAAY,EACZ,UAAmB,IAAI,EACvB,YAAqB,EACrB,OAAiB,EACX;QACN,MAAM,QAAuB;YAC3B,IAAI,eAAe,mBAAmB,CAAC;YACvC,WAAW,IAAI;YACf;YACA;YACA;YACA;YACA,YAAY,QAAQ,EAAE,IAAI,QAAQ,MAAM,IAAI,QAAQ,SAAS;YAC7D,SAAS,IAAI,CAAC,eAAe,CAAC;YAC9B,WAAW,IAAI,CAAC,WAAW,CAAC;YAC5B,WAAW,SAAS,QAAQ,IAAI,iBAAiB;YACjD;YACA;QACF;QAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEnB,oCAAoC;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,gBAAgB,KAAK,CAAC,UAAU,EAAE;YAC3D,IAAI,CAAC,QAAQ,CAAC,KAAK;QACrB;QAEA,6FAA6F;QAC7F,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,MAAM,SAAS,CAAC,WAAW,GAAG,GAAG,EAAE,MAAM,MAAM,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,MAAM,SAAS,CAAC,GAAG,EAAE,MAAM,OAAO,GAAG,YAAY,UAAU;IAC/J;IAEA;;GAEC,GACD,YAAY,MAAe,EAAE,QAAgB,GAAG,EAAmB;QACjE,IAAI,cAAc,IAAI,CAAC,QAAQ;QAE/B,IAAI,QAAQ;YACV,cAAc,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAC7D;QAEA,OAAO,YACJ,KAAK,CAAC,CAAC,OACP,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,GAAG,KAAK;gBACR,SAAS,IAAI,CAAC,eAAe,CAAC,MAAM,OAAO;YAC7C,CAAC;IACL;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAY,EAAO;QACzC,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;YAC3C,OAAO;QACT;QAEA,MAAM,YAAY;YAAE,GAAG,OAAO;QAAC;QAE/B,gBAAgB,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAC5C,IAAI,SAAS,CAAC,MAAM,EAAE;gBACpB,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,MAAM;YAC5D;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAAkB,IAAY,EAAU;QAC9C,IAAI,KAAK,MAAM,IAAI,GAAG;YACpB,OAAO;QACT;QACA,OAAO,KAAK,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,KAAK,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC,KAAK,MAAM,GAAG;IAC3F;IAEA;;GAEC,GACD,AAAQ,YAAY,OAAiB,EAAsB;QACzD,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,IAAI,WAAW;YACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;QACrC;QAEA,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,OAAO;IACT;AACF;AAGO,MAAM;IACX,OAAe,SAAsB;IAC7B,gBAAmE,IAAI,MAAM;IAC7E,gBAAmE,IAAI,MAAM;IAErF,aAAsB,CAAC;IAEvB,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA;;GAEC,GACD,eAAe,MAAc,EAAE,mBAA4B,KAAK,EAA4C;QAC1G,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,mBACX;YAAE,KAAK,gBAAgB,SAAS,CAAC,yBAAyB;YAAE,QAAQ,KAAK,KAAK;QAAK,IACnF;YAAE,KAAK,gBAAgB,SAAS,CAAC,oBAAoB;YAAE,QAAQ,KAAK;QAAK;QAE7E,MAAM,SAAS,mBAAmB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;QACzE,MAAM,YAAY,OAAO,GAAG,CAAC;QAE7B,IAAI,CAAC,aAAa,MAAM,UAAU,SAAS,EAAE;YAC3C,8BAA8B;YAC9B,OAAO,GAAG,CAAC,QAAQ;gBAAE,OAAO;gBAAG,WAAW,MAAM,OAAO,MAAM;YAAC;YAC9D,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,IAAI,UAAU,KAAK,IAAI,OAAO,GAAG,EAAE;YACjC,OAAO;gBAAE,SAAS;gBAAO,WAAW,UAAU,SAAS;YAAC;QAC1D;QAEA,UAAU,KAAK;QACf,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,KAAK,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAI;YACzD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC5B;QACF;QAEA,KAAK,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,GAAI;YACzD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC5B;QACF;IACF;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,eAAe,MAAW,EAAU;QACzC,IAAI,OAAO,WAAW,UAAU;YAC9B,IAAI,CAAC,SAAS,WAAW,SAAS,GAAG;gBACnC,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,KAAK,KAAK,CAAC,SAAS,OAAO,KAAK,4BAA4B;QACrE;QAEA,IAAI,OAAO,WAAW,UAAU;YAC9B,MAAM,SAAS,WAAW,OAAO,OAAO,CAAC,YAAY;YACrD,IAAI,MAAM,WAAW,SAAS,GAAG;gBAC/B,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,KAAK,KAAK,CAAC,SAAS,OAAO;QACpC;QAEA,MAAM,IAAI,MAAM;IAClB;IAEA;;GAEC,GACD,OAAO,aAAa,IAAY,EAAE,YAAoB,IAAI,EAAU;QAClE,IAAI,OAAO,SAAS,UAAU;YAC5B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,MAAM,YAAY,KACf,OAAO,CAAC,uDAAuD,IAC/D,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,eAAe,IACvB,IAAI;QAEP,IAAI,UAAU,MAAM,GAAG,WAAW;YAChC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,UAAU,mBAAmB,CAAC;QAC3E;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAc,EAAU;QACnD,MAAM,eAAe;YAAC;YAAQ;YAAQ;YAAU;YAAU;YAAY;SAAc;QAEpF,IAAI,CAAC,aAAa,QAAQ,CAAC,SAAS;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBAAsB,aAAqB,EAAU;QAC1D,IAAI,OAAO,kBAAkB,UAAU;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,+DAA+D;QAC/D,MAAM,YAAY,cAAc,OAAO,CAAC,oBAAoB;QAE5D,IAAI,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,KAAK;YAClD,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT;AACF;AAGO,MAAM,cAAc,YAAY,WAAW;AAC3C,MAAM,cAAc,YAAY,WAAW;AAG3C,MAAM,kBAAkB;IAC7B,YAAY,OAAO;AACrB", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/app/api/payments/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { auth } from '@clerk/nextjs/server';\nimport { paymentFormSchema } from '@/lib/validation/billing-schemas';\nimport { formatValidationErrors } from '@/lib/validation/billing-schemas';\nimport { validateBusinessRules } from '@/lib/validation/validation-utils';\nimport { auditLogger, rateLimiter, InputSanitizer } from '@/lib/billing-security';\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n\n// Enhanced error handling utility\nclass APIError extends Error {\n  constructor(\n    message: string,\n    public status: number = 500,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'APIError';\n  }\n}\n\n// Utility to get user info from Clerk with error handling\nasync function getClerkUserInfo(userId: string) {\n  try {\n    const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    });\n\n    if (!response.ok) {\n      throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching Clerk user:', error);\n    throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');\n  }\n}\n\n// Utility to make backend requests with comprehensive error handling\nasync function makeBackendRequest(url: string, options: RequestInit, userId: string, userEmail: string) {\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': userEmail,\n        ...options.headers,\n      },\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new APIError(\n        data.error || `Backend request failed: ${response.status}`,\n        response.status,\n        data.code || 'BACKEND_ERROR',\n        data\n      );\n    }\n\n    return data;\n  } catch (error) {\n    if (error instanceof APIError) {\n      throw error;\n    }\n\n    console.error('Backend request error:', error);\n    throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');\n  }\n}\n\n/**\n * GET /api/payments - Proxy to backend payments API\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    // Get user info from Clerk\n    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    }).then(res => res.json());\n\n    // Forward request to backend with authentication headers\n    const url = new URL(request.url);\n    const backendUrl = `${BACKEND_URL}/api/payments${url.search}`;\n    \n    const response = await fetch(backendUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': user.email_addresses[0]?.email_address || '',\n      },\n    });\n\n    const data = await response.json();\n    \n    if (!response.ok) {\n      return NextResponse.json(data, { status: response.status });\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Error proxying payments request:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * POST /api/payments - Process payment with comprehensive validation and business logic\n */\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      auditLogger.logFinancialOperation(\n        'anonymous',\n        'anonymous',\n        'CREATE_PAYMENT_UNAUTHORIZED',\n        'payments',\n        { endpoint: '/api/payments' },\n        false,\n        'Authentication required',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Authentication required',\n          code: 'AUTH_REQUIRED',\n          message: '请先登录以处理支付'\n        },\n        { status: 401 }\n      );\n    }\n\n    // Check rate limiting for payment requests (more restrictive)\n    const rateLimitResult = rateLimiter.checkRateLimit(userId, true);\n    if (!rateLimitResult.allowed) {\n      auditLogger.logFinancialOperation(\n        userId,\n        'unknown',\n        'CREATE_PAYMENT_RATE_LIMITED',\n        'payments',\n        { endpoint: '/api/payments', resetTime: rateLimitResult.resetTime },\n        false,\n        'Payment rate limit exceeded',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Payment rate limit exceeded',\n          code: 'PAYMENT_RATE_LIMIT_EXCEEDED',\n          message: '支付请求过于频繁，请稍后重试',\n          resetTime: rateLimitResult.resetTime\n        },\n        { status: 429 }\n      );\n    }\n\n    // Parse and validate request body\n    let body;\n    try {\n      body = await request.json();\n    } catch (error) {\n      return NextResponse.json(\n        {\n          error: 'Invalid JSON in request body',\n          code: 'INVALID_JSON',\n          message: '请求数据格式错误'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Sanitize payment input data\n    try {\n      body.amount = InputSanitizer.sanitizeAmount(body.amount);\n      body.paymentMethod = InputSanitizer.sanitizePaymentMethod(body.paymentMethod);\n\n      if (body.transactionId) {\n        body.transactionId = InputSanitizer.sanitizeText(body.transactionId, 100);\n      }\n      if (body.notes) {\n        body.notes = InputSanitizer.sanitizeText(body.notes, 500);\n      }\n    } catch (sanitizationError) {\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_PAYMENT_SANITIZATION_FAILED',\n        'payments',\n        { error: sanitizationError },\n        false,\n        sanitizationError instanceof Error ? sanitizationError.message : 'Payment sanitization failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Input sanitization failed',\n          code: 'SANITIZATION_ERROR',\n          message: '支付数据格式不正确'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Validate payment data using Zod schema\n    const validationResult = paymentFormSchema.safeParse(body);\n    if (!validationResult.success) {\n      const formattedErrors = formatValidationErrors(validationResult.error);\n\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_PAYMENT_VALIDATION_FAILED',\n        'payments',\n        { validationErrors: formattedErrors },\n        false,\n        'Payment validation failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Validation failed',\n          code: 'VALIDATION_ERROR',\n          message: '支付数据验证失败',\n          details: formattedErrors\n        },\n        { status: 400 }\n      );\n    }\n\n    const validatedData = validationResult.data;\n\n    // Get user info from Clerk with error handling\n    const user = await getClerkUserInfo(userId);\n    const userEmail = user.email_addresses[0]?.email_address || '';\n\n    if (!userEmail) {\n      return NextResponse.json(\n        {\n          error: 'User email not found',\n          code: 'USER_EMAIL_MISSING',\n          message: '用户邮箱信息缺失，请联系管理员'\n        },\n        { status: 400 }\n      );\n    }\n\n    // If bill ID is provided, validate payment amount against bill\n    if (body.billId) {\n      try {\n        // Fetch bill details to validate payment amount\n        const billUrl = `${BACKEND_URL}/api/bills/${body.billId}`;\n        const bill = await makeBackendRequest(billUrl, { method: 'GET' }, userId, userEmail);\n\n        // Validate payment amount against remaining bill amount\n        const paymentValidation = validateBusinessRules.isValidPaymentAmount(validatedData.amount, bill);\n        if (!paymentValidation.valid) {\n          return NextResponse.json(\n            {\n              error: 'Invalid payment amount',\n              code: 'INVALID_PAYMENT_AMOUNT',\n              message: paymentValidation.reason\n            },\n            { status: 400 }\n          );\n        }\n      } catch (error) {\n        if (error instanceof APIError && error.status === 404) {\n          return NextResponse.json(\n            {\n              error: 'Bill not found',\n              code: 'BILL_NOT_FOUND',\n              message: '指定的账单不存在'\n            },\n            { status: 404 }\n          );\n        }\n        throw error; // Re-throw other errors\n      }\n    }\n\n    // Additional business logic validation for payment methods\n    const paymentMethodValidation = validatePaymentMethod(validatedData);\n    if (!paymentMethodValidation.valid) {\n      return NextResponse.json(\n        {\n          error: 'Payment method validation failed',\n          code: 'PAYMENT_METHOD_ERROR',\n          message: paymentMethodValidation.reason\n        },\n        { status: 400 }\n      );\n    }\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/payments`;\n    const data = await makeBackendRequest(\n      backendUrl,\n      {\n        method: 'POST',\n        body: JSON.stringify(validatedData)\n      },\n      userId,\n      userEmail\n    );\n\n    // Log successful payment creation (mask sensitive data)\n    auditLogger.logFinancialOperation(\n      userId,\n      userEmail,\n      'CREATE_PAYMENT',\n      'payments',\n      {\n        paymentId: data.id,\n        billId: validatedData.billId,\n        amount: validatedData.amount,\n        paymentMethod: validatedData.paymentMethod,\n        transactionId: validatedData.transactionId, // Will be masked by audit logger\n        patientId: validatedData.patientId\n      },\n      true,\n      undefined,\n      request\n    );\n\n    return NextResponse.json(data, { status: 201 });\n  } catch (error) {\n    if (error instanceof APIError) {\n      return NextResponse.json(\n        {\n          error: error.message,\n          code: error.code,\n          message: error.message,\n          details: error.details\n        },\n        { status: error.status }\n      );\n    }\n\n    console.error('Unexpected error in POST /api/payments:', error);\n    return NextResponse.json(\n      {\n        error: 'Internal server error',\n        code: 'INTERNAL_ERROR',\n        message: '处理支付时发生服务器错误，请稍后重试'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Additional payment method validation\nfunction validatePaymentMethod(paymentData: any): { valid: boolean; reason?: string } {\n  const { paymentMethod, transactionId, amount } = paymentData;\n\n  // Validate transaction ID requirements for different payment methods\n  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];\n  if (methodsRequiringTransactionId.includes(paymentMethod)) {\n    if (!transactionId || transactionId.trim().length === 0) {\n      return {\n        valid: false,\n        reason: `${getPaymentMethodName(paymentMethod)}需要提供交易ID`\n      };\n    }\n  }\n\n  // Validate amount limits for different payment methods\n  if (paymentMethod === 'cash' && amount > 50000) {\n    return {\n      valid: false,\n      reason: '现金支付单笔金额不能超过50,000元'\n    };\n  }\n\n  if (paymentMethod === 'wechat' && amount > 200000) {\n    return {\n      valid: false,\n      reason: '微信支付单笔金额不能超过200,000元'\n    };\n  }\n\n  if (paymentMethod === 'alipay' && amount > 200000) {\n    return {\n      valid: false,\n      reason: '支付宝单笔金额不能超过200,000元'\n    };\n  }\n\n  return { valid: true };\n}\n\n// Helper function to get payment method display name\nfunction getPaymentMethodName(method: string): string {\n  const methods: Record<string, string> = {\n    cash: '现金',\n    card: '银行卡',\n    wechat: '微信支付',\n    alipay: '支付宝',\n    transfer: '银行转账',\n    installment: '分期付款',\n  };\n  return methods[method] || method;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AACA;;;;;;;AAEA,MAAM,cAAc,6DAAmC;AAEvD,kCAAkC;AAClC,MAAM,iBAAiB;;;;IACrB,YACE,OAAe,EACf,AAAO,SAAiB,GAAG,EAC3B,AAAO,IAAa,EACpB,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eAJC,SAAA,aACA,OAAA,WACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,0DAA0D;AAC1D,eAAe,iBAAiB,MAAc;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YACvE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACzD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,SAAS,oCAAoC,KAAK;QAC9D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM,IAAI,SAAS,sCAAsC,KAAK;IAChE;AACF;AAEA,qEAAqE;AACrE,eAAe,mBAAmB,GAAW,EAAE,OAAoB,EAAE,MAAc,EAAE,SAAiB;IACpG,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,GAAG,OAAO;YACV,SAAS;gBACP,gBAAgB;gBAChB,mBAAmB;gBACnB,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,SACR,KAAK,KAAK,IAAI,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE,EAC1D,SAAS,MAAM,EACf,KAAK,IAAI,IAAI,iBACb;QAEJ;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,UAAU;YAC7B,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,SAAS,+BAA+B,KAAK;IACzD;AACF;AAKO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,OAAO,MAAM,MAAM,CAAC,+BAA+B,EAAE,QAAQ,EAAE;YACnE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACzD;QACF,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;QAEvB,yDAAyD;QACzD,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,aAAa,GAAG,YAAY,aAAa,EAAE,IAAI,MAAM,EAAE;QAE7D,MAAM,WAAW,MAAM,MAAM,YAAY;YACvC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,mBAAmB;gBACnB,gBAAgB,KAAK,eAAe,CAAC,EAAE,EAAE,iBAAiB;YAC5D;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAC3D;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAKO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,+RAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,aACA,aACA,+BACA,YACA;gBAAE,UAAU;YAAgB,GAC5B,OACA,2BACA;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,8DAA8D;QAC9D,MAAM,kBAAkB,mIAAA,CAAA,cAAW,CAAC,cAAc,CAAC,QAAQ;QAC3D,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,+BACA,YACA;gBAAE,UAAU;gBAAiB,WAAW,gBAAgB,SAAS;YAAC,GAClE,OACA,+BACA;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,WAAW,gBAAgB,SAAS;YACtC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,QAAQ,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,IAAI;YACF,KAAK,MAAM,GAAG,mIAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,KAAK,MAAM;YACvD,KAAK,aAAa,GAAG,mIAAA,CAAA,iBAAc,CAAC,qBAAqB,CAAC,KAAK,aAAa;YAE5E,IAAI,KAAK,aAAa,EAAE;gBACtB,KAAK,aAAa,GAAG,mIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,KAAK,aAAa,EAAE;YACvE;YACA,IAAI,KAAK,KAAK,EAAE;gBACd,KAAK,KAAK,GAAG,mIAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,KAAK,KAAK,EAAE;YACvD;QACF,EAAE,OAAO,mBAAmB;YAC1B,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,sCACA,YACA;gBAAE,OAAO;YAAkB,GAC3B,OACA,6BAA6B,QAAQ,kBAAkB,OAAO,GAAG,+BACjE;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,yCAAyC;QACzC,MAAM,mBAAmB,gJAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC;QACrD,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B,MAAM,kBAAkB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD,EAAE,iBAAiB,KAAK;YAErE,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,oCACA,YACA;gBAAE,kBAAkB;YAAgB,GACpC,OACA,6BACA;YAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,+CAA+C;QAC/C,MAAM,OAAO,MAAM,iBAAiB;QACpC,MAAM,YAAY,KAAK,eAAe,CAAC,EAAE,EAAE,iBAAiB;QAE5D,IAAI,CAAC,WAAW;YACd,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS;YACX,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,+DAA+D;QAC/D,IAAI,KAAK,MAAM,EAAE;YACf,IAAI;gBACF,gDAAgD;gBAChD,MAAM,UAAU,GAAG,YAAY,WAAW,EAAE,KAAK,MAAM,EAAE;gBACzD,MAAM,OAAO,MAAM,mBAAmB,SAAS;oBAAE,QAAQ;gBAAM,GAAG,QAAQ;gBAE1E,wDAAwD;gBACxD,MAAM,oBAAoB,iJAAA,CAAA,wBAAqB,CAAC,oBAAoB,CAAC,cAAc,MAAM,EAAE;gBAC3F,IAAI,CAAC,kBAAkB,KAAK,EAAE;oBAC5B,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBACE,OAAO;wBACP,MAAM;wBACN,SAAS,kBAAkB,MAAM;oBACnC,GACA;wBAAE,QAAQ;oBAAI;gBAElB;YACF,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,YAAY,MAAM,MAAM,KAAK,KAAK;oBACrD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBACE,OAAO;wBACP,MAAM;wBACN,SAAS;oBACX,GACA;wBAAE,QAAQ;oBAAI;gBAElB;gBACA,MAAM,OAAO,wBAAwB;YACvC;QACF;QAEA,2DAA2D;QAC3D,MAAM,0BAA0B,sBAAsB;QACtD,IAAI,CAAC,wBAAwB,KAAK,EAAE;YAClC,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,MAAM;gBACN,SAAS,wBAAwB,MAAM;YACzC,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,yDAAyD;QACzD,MAAM,aAAa,GAAG,YAAY,aAAa,CAAC;QAChD,MAAM,OAAO,MAAM,mBACjB,YACA;YACE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB,GACA,QACA;QAGF,wDAAwD;QACxD,mIAAA,CAAA,cAAW,CAAC,qBAAqB,CAC/B,QACA,WACA,kBACA,YACA;YACE,WAAW,KAAK,EAAE;YAClB,QAAQ,cAAc,MAAM;YAC5B,QAAQ,cAAc,MAAM;YAC5B,eAAe,cAAc,aAAa;YAC1C,eAAe,cAAc,aAAa;YAC1C,WAAW,cAAc,SAAS;QACpC,GACA,MACA,WACA;QAGF,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,UAAU;YAC7B,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO,MAAM,OAAO;gBACpB,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,SAAS,MAAM,OAAO;YACxB,GACA;gBAAE,QAAQ,MAAM,MAAM;YAAC;QAE3B;QAEA,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,MAAM;YACN,SAAS;QACX,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,uCAAuC;AACvC,SAAS,sBAAsB,WAAgB;IAC7C,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG;IAEjD,qEAAqE;IACrE,MAAM,gCAAgC;QAAC;QAAQ;QAAU;QAAU;KAAW;IAC9E,IAAI,8BAA8B,QAAQ,CAAC,gBAAgB;QACzD,IAAI,CAAC,iBAAiB,cAAc,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,OAAO;gBACL,OAAO;gBACP,QAAQ,GAAG,qBAAqB,eAAe,QAAQ,CAAC;YAC1D;QACF;IACF;IAEA,uDAAuD;IACvD,IAAI,kBAAkB,UAAU,SAAS,OAAO;QAC9C,OAAO;YACL,OAAO;YACP,QAAQ;QACV;IACF;IAEA,IAAI,kBAAkB,YAAY,SAAS,QAAQ;QACjD,OAAO;YACL,OAAO;YACP,QAAQ;QACV;IACF;IAEA,IAAI,kBAAkB,YAAY,SAAS,QAAQ;QACjD,OAAO;YACL,OAAO;YACP,QAAQ;QACV;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAEA,qDAAqD;AACrD,SAAS,qBAAqB,MAAc;IAC1C,MAAM,UAAkC;QACtC,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,aAAa;IACf;IACA,OAAO,OAAO,CAAC,OAAO,IAAI;AAC5B", "debugId": null}}]}