{"node": {"7f693a86fb4786515f422107e0381cb9b1171d4002": {"workers": {"app/_not-found/page": {"moduleId": "[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/appointments/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/appointments/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/billing/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/billing/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/patients/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/patients/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/treatments/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/treatments/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/_not-found/page": "action-browser", "app/dashboard/appointments/page": "action-browser", "app/dashboard/billing/page": "action-browser", "app/dashboard/patients/page": "action-browser", "app/dashboard/treatments/page": "action-browser"}}, "7fcc914e0aa2ab4b8cee6cb810a569d028b2d20c74": {"workers": {"app/_not-found/page": {"moduleId": "[project]/.next-internal/server/app/_not-found/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/appointments/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/appointments/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/billing/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/billing/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/patients/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/patients/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}, "app/dashboard/treatments/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/treatments/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/_not-found/page": "action-browser", "app/dashboard/appointments/page": "action-browser", "app/dashboard/billing/page": "action-browser", "app/dashboard/patients/page": "action-browser", "app/dashboard/treatments/page": "action-browser"}}}, "edge": {}, "encryptionKey": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw="}