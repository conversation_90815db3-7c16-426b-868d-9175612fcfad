{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/scroll-area.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction ScrollArea({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\r\n  return (\r\n    <ScrollAreaPrimitive.Root\r\n      data-slot='scroll-area'\r\n      className={cn('relative', className)}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.Viewport\r\n        data-slot='scroll-area-viewport'\r\n        className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1'\r\n      >\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar />\r\n      <ScrollAreaPrimitive.Corner />\r\n    </ScrollAreaPrimitive.Root>\r\n  );\r\n}\r\n\r\nfunction ScrollBar({\r\n  className,\r\n  orientation = 'vertical',\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\r\n  return (\r\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n      data-slot='scroll-area-scrollbar'\r\n      orientation={orientation}\r\n      className={cn(\r\n        'flex touch-none p-px transition-colors select-none',\r\n        orientation === 'vertical' &&\r\n          'h-full w-2.5 border-l border-l-transparent',\r\n        orientation === 'horizontal' &&\r\n          'h-2.5 flex-col border-t border-t-transparent',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.ScrollAreaThumb\r\n        data-slot='scroll-area-thumb'\r\n        className='bg-border relative flex-1 rounded-full'\r\n      />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n  );\r\n}\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6VAAC,iRAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,6VAAC,iRAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,6VAAC;;;;;0BACD,6VAAC,iRAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,6VAAC,iRAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,6VAAC,iRAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot='tabs'\r\n      className={cn('flex flex-col gap-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot='tabs-list'\r\n      className={cn(\r\n        'bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot='tabs-trigger'\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot='tabs-content'\r\n      className={cn('flex-1 outline-none', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6VAAC,gRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6VAAC,gRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,gRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,gRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='badge'\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/billing-error-handler.ts"], "sourcesContent": ["// Comprehensive error handling system for billing operations\n// Provides consistent error handling, logging, and user feedback\n\nimport { toast } from 'sonner';\n\n// Error types and interfaces\nexport interface BillingError {\n  code: string;\n  message: string;\n  userMessage: string;\n  severity: 'error' | 'warning' | 'info';\n  details?: any;\n  timestamp: Date;\n  context?: string;\n}\n\nexport interface ErrorHandlerOptions {\n  showToast?: boolean;\n  logError?: boolean;\n  context?: string;\n  fallbackMessage?: string;\n}\n\n// Error code mappings with user-friendly messages\nconst ERROR_MESSAGES: Record<string, { message: string; severity: 'error' | 'warning' | 'info' }> = {\n  // Authentication errors\n  AUTH_REQUIRED: { message: '请先登录以继续操作', severity: 'error' },\n  AUTH_SERVICE_ERROR: { message: '认证服务暂时不可用，请稍后重试', severity: 'error' },\n  CLERK_USER_FETCH_ERROR: { message: '获取用户信息失败，请重新登录', severity: 'error' },\n  USER_EMAIL_MISSING: { message: '用户邮箱信息缺失，请联系管理员', severity: 'error' },\n\n  // Validation errors\n  VALIDATION_ERROR: { message: '输入数据验证失败，请检查表单内容', severity: 'error' },\n  INVALID_JSON: { message: '数据格式错误，请刷新页面重试', severity: 'error' },\n  INVALID_LIMIT: { message: '分页参数错误', severity: 'warning' },\n  INVALID_PAGE: { message: '页码参数错误', severity: 'warning' },\n  SUBTOTAL_MISMATCH: { message: '账单金额计算错误，请重新检查', severity: 'error' },\n\n  // Business logic errors\n  INVALID_PAYMENT_AMOUNT: { message: '支付金额无效', severity: 'error' },\n  BILL_NOT_FOUND: { message: '账单不存在或已被删除', severity: 'error' },\n  PAYMENT_METHOD_ERROR: { message: '支付方式验证失败', severity: 'error' },\n  INSUFFICIENT_PERMISSIONS: { message: '权限不足，无法执行此操作', severity: 'error' },\n\n  // Backend/Service errors\n  BACKEND_ERROR: { message: '后端服务错误', severity: 'error' },\n  BACKEND_SERVICE_ERROR: { message: '后端服务暂时不可用，请稍后重试', severity: 'error' },\n  DATABASE_ERROR: { message: '数据库操作失败，请稍后重试', severity: 'error' },\n  NETWORK_ERROR: { message: '网络连接错误，请检查网络连接', severity: 'error' },\n\n  // Generic errors\n  INTERNAL_ERROR: { message: '系统内部错误，请稍后重试', severity: 'error' },\n  UNKNOWN_ERROR: { message: '发生未知错误，请联系技术支持', severity: 'error' },\n};\n\n// Error handler class\nexport class BillingErrorHandler {\n  private static instance: BillingErrorHandler;\n  private errorLog: BillingError[] = [];\n\n  private constructor() {}\n\n  static getInstance(): BillingErrorHandler {\n    if (!BillingErrorHandler.instance) {\n      BillingErrorHandler.instance = new BillingErrorHandler();\n    }\n    return BillingErrorHandler.instance;\n  }\n\n  /**\n   * Handle API response errors\n   */\n  handleAPIError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const {\n      showToast = true,\n      logError = true,\n      context = 'API',\n      fallbackMessage = '操作失败，请稍后重试'\n    } = options;\n\n    let billingError: BillingError;\n\n    if (error?.code && ERROR_MESSAGES[error.code]) {\n      const errorInfo = ERROR_MESSAGES[error.code];\n      billingError = {\n        code: error.code,\n        message: error.message || errorInfo.message,\n        userMessage: error.message || errorInfo.message,\n        severity: errorInfo.severity,\n        details: error.details,\n        timestamp: new Date(),\n        context\n      };\n    } else {\n      // Handle unknown errors\n      billingError = {\n        code: 'UNKNOWN_ERROR',\n        message: error?.message || 'Unknown error occurred',\n        userMessage: fallbackMessage,\n        severity: 'error',\n        details: error,\n        timestamp: new Date(),\n        context\n      };\n    }\n\n    if (logError) {\n      this.logError(billingError);\n    }\n\n    if (showToast) {\n      this.showErrorToast(billingError);\n    }\n\n    return billingError;\n  }\n\n  /**\n   * Handle network/fetch errors\n   */\n  handleNetworkError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const networkError: BillingError = {\n      code: 'NETWORK_ERROR',\n      message: error?.message || 'Network request failed',\n      userMessage: '网络连接错误，请检查网络连接后重试',\n      severity: 'error',\n      details: error,\n      timestamp: new Date(),\n      context: options.context || 'Network'\n    };\n\n    if (options.logError !== false) {\n      this.logError(networkError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(networkError);\n    }\n\n    return networkError;\n  }\n\n  /**\n   * Handle validation errors\n   */\n  handleValidationError(validationErrors: any[], options: ErrorHandlerOptions = {}): BillingError {\n    const firstError = validationErrors[0];\n    const validationError: BillingError = {\n      code: 'VALIDATION_ERROR',\n      message: 'Validation failed',\n      userMessage: firstError?.message || '表单验证失败，请检查输入内容',\n      severity: 'error',\n      details: validationErrors,\n      timestamp: new Date(),\n      context: options.context || 'Validation'\n    };\n\n    if (options.logError !== false) {\n      this.logError(validationError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(validationError);\n    }\n\n    return validationError;\n  }\n\n  /**\n   * Show success message\n   */\n  showSuccess(message: string, description?: string): void {\n    toast.success(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Show warning message\n   */\n  showWarning(message: string, description?: string): void {\n    toast.warning(message, {\n      description,\n      duration: 4000,\n    });\n  }\n\n  /**\n   * Show info message\n   */\n  showInfo(message: string, description?: string): void {\n    toast.info(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Log error to console and internal log\n   */\n  private logError(error: BillingError): void {\n    console.error(`[${error.context}] ${error.code}: ${error.message}`, {\n      userMessage: error.userMessage,\n      details: error.details,\n      timestamp: error.timestamp\n    });\n\n    // Add to internal error log (keep last 100 errors)\n    this.errorLog.push(error);\n    if (this.errorLog.length > 100) {\n      this.errorLog.shift();\n    }\n  }\n\n  /**\n   * Show error toast notification\n   */\n  private showErrorToast(error: BillingError): void {\n    const toastOptions = {\n      duration: error.severity === 'error' ? 5000 : 4000,\n    };\n\n    switch (error.severity) {\n      case 'error':\n        toast.error(error.userMessage, toastOptions);\n        break;\n      case 'warning':\n        toast.warning(error.userMessage, toastOptions);\n        break;\n      case 'info':\n        toast.info(error.userMessage, toastOptions);\n        break;\n    }\n  }\n\n  /**\n   * Get error log for debugging\n   */\n  getErrorLog(): BillingError[] {\n    return [...this.errorLog];\n  }\n\n  /**\n   * Clear error log\n   */\n  clearErrorLog(): void {\n    this.errorLog = [];\n  }\n}\n\n// Convenience functions for common error handling patterns\nexport const billingErrorHandler = BillingErrorHandler.getInstance();\n\nexport const handleAPIError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleAPIError(error, options);\n\nexport const handleNetworkError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleNetworkError(error, options);\n\nexport const handleValidationError = (errors: any[], options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleValidationError(errors, options);\n\nexport const showSuccess = (message: string, description?: string) => \n  billingErrorHandler.showSuccess(message, description);\n\nexport const showWarning = (message: string, description?: string) => \n  billingErrorHandler.showWarning(message, description);\n\nexport const showInfo = (message: string, description?: string) => \n  billingErrorHandler.showInfo(message, description);\n\n// Error boundary helper for React components\nexport const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(\n  fn: T,\n  context?: string\n): T => {\n  return (async (...args: any[]) => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      handleAPIError(error, { context });\n      throw error;\n    }\n  }) as T;\n};\n\n// Retry utility with exponential backoff\nexport const retryWithBackoff = async <T>(\n  fn: () => Promise<T>,\n  maxRetries: number = 3,\n  baseDelay: number = 1000,\n  context?: string\n): Promise<T> => {\n  let lastError: any;\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      return await fn();\n    } catch (error) {\n      lastError = error;\n      \n      if (attempt === maxRetries) {\n        handleAPIError(error, { \n          context: context || 'Retry',\n          fallbackMessage: `操作失败，已重试${maxRetries}次`\n        });\n        throw error;\n      }\n\n      // Exponential backoff delay\n      const delay = baseDelay * Math.pow(2, attempt - 1);\n      await new Promise(resolve => setTimeout(resolve, delay));\n    }\n  }\n\n  throw lastError;\n};\n"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,iEAAiE;;;;;;;;;;;;;AAEjE;;AAoBA,kDAAkD;AAClD,MAAM,iBAA8F;IAClG,wBAAwB;IACxB,eAAe;QAAE,SAAS;QAAa,UAAU;IAAQ;IACzD,oBAAoB;QAAE,SAAS;QAAmB,UAAU;IAAQ;IACpE,wBAAwB;QAAE,SAAS;QAAkB,UAAU;IAAQ;IACvE,oBAAoB;QAAE,SAAS;QAAmB,UAAU;IAAQ;IAEpE,oBAAoB;IACpB,kBAAkB;QAAE,SAAS;QAAoB,UAAU;IAAQ;IACnE,cAAc;QAAE,SAAS;QAAkB,UAAU;IAAQ;IAC7D,eAAe;QAAE,SAAS;QAAU,UAAU;IAAU;IACxD,cAAc;QAAE,SAAS;QAAU,UAAU;IAAU;IACvD,mBAAmB;QAAE,SAAS;QAAkB,UAAU;IAAQ;IAElE,wBAAwB;IACxB,wBAAwB;QAAE,SAAS;QAAU,UAAU;IAAQ;IAC/D,gBAAgB;QAAE,SAAS;QAAc,UAAU;IAAQ;IAC3D,sBAAsB;QAAE,SAAS;QAAY,UAAU;IAAQ;IAC/D,0BAA0B;QAAE,SAAS;QAAgB,UAAU;IAAQ;IAEvE,yBAAyB;IACzB,eAAe;QAAE,SAAS;QAAU,UAAU;IAAQ;IACtD,uBAAuB;QAAE,SAAS;QAAmB,UAAU;IAAQ;IACvE,gBAAgB;QAAE,SAAS;QAAiB,UAAU;IAAQ;IAC9D,eAAe;QAAE,SAAS;QAAkB,UAAU;IAAQ;IAE9D,iBAAiB;IACjB,gBAAgB;QAAE,SAAS;QAAgB,UAAU;IAAQ;IAC7D,eAAe;QAAE,SAAS;QAAkB,UAAU;IAAQ;AAChE;AAGO,MAAM;IACX,OAAe,SAA8B;IACrC,WAA2B,EAAE,CAAC;IAEtC,aAAsB,CAAC;IAEvB,OAAO,cAAmC;QACxC,IAAI,CAAC,oBAAoB,QAAQ,EAAE;YACjC,oBAAoB,QAAQ,GAAG,IAAI;QACrC;QACA,OAAO,oBAAoB,QAAQ;IACrC;IAEA;;GAEC,GACD,eAAe,KAAU,EAAE,UAA+B,CAAC,CAAC,EAAgB;QAC1E,MAAM,EACJ,YAAY,IAAI,EAChB,WAAW,IAAI,EACf,UAAU,KAAK,EACf,kBAAkB,YAAY,EAC/B,GAAG;QAEJ,IAAI;QAEJ,IAAI,OAAO,QAAQ,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE;YAC7C,MAAM,YAAY,cAAc,CAAC,MAAM,IAAI,CAAC;YAC5C,eAAe;gBACb,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO,IAAI,UAAU,OAAO;gBAC3C,aAAa,MAAM,OAAO,IAAI,UAAU,OAAO;gBAC/C,UAAU,UAAU,QAAQ;gBAC5B,SAAS,MAAM,OAAO;gBACtB,WAAW,IAAI;gBACf;YACF;QACF,OAAO;YACL,wBAAwB;YACxB,eAAe;gBACb,MAAM;gBACN,SAAS,OAAO,WAAW;gBAC3B,aAAa;gBACb,UAAU;gBACV,SAAS;gBACT,WAAW,IAAI;gBACf;YACF;QACF;QAEA,IAAI,UAAU;YACZ,IAAI,CAAC,QAAQ,CAAC;QAChB;QAEA,IAAI,WAAW;YACb,IAAI,CAAC,cAAc,CAAC;QACtB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,KAAU,EAAE,UAA+B,CAAC,CAAC,EAAgB;QAC9E,MAAM,eAA6B;YACjC,MAAM;YACN,SAAS,OAAO,WAAW;YAC3B,aAAa;YACb,UAAU;YACV,SAAS;YACT,WAAW,IAAI;YACf,SAAS,QAAQ,OAAO,IAAI;QAC9B;QAEA,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC9B,IAAI,CAAC,QAAQ,CAAC;QAChB;QAEA,IAAI,QAAQ,SAAS,KAAK,OAAO;YAC/B,IAAI,CAAC,cAAc,CAAC;QACtB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,sBAAsB,gBAAuB,EAAE,UAA+B,CAAC,CAAC,EAAgB;QAC9F,MAAM,aAAa,gBAAgB,CAAC,EAAE;QACtC,MAAM,kBAAgC;YACpC,MAAM;YACN,SAAS;YACT,aAAa,YAAY,WAAW;YACpC,UAAU;YACV,SAAS;YACT,WAAW,IAAI;YACf,SAAS,QAAQ,OAAO,IAAI;QAC9B;QAEA,IAAI,QAAQ,QAAQ,KAAK,OAAO;YAC9B,IAAI,CAAC,QAAQ,CAAC;QAChB;QAEA,IAAI,QAAQ,SAAS,KAAK,OAAO;YAC/B,IAAI,CAAC,cAAc,CAAC;QACtB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,YAAY,OAAe,EAAE,WAAoB,EAAQ;QACvD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS;YACrB;YACA,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,YAAY,OAAe,EAAE,WAAoB,EAAQ;QACvD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS;YACrB;YACA,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,SAAS,OAAe,EAAE,WAAoB,EAAQ;QACpD,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,SAAS;YAClB;YACA,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,AAAQ,SAAS,KAAmB,EAAQ;QAC1C,QAAQ,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,MAAM,OAAO,EAAE,EAAE;YAClE,aAAa,MAAM,WAAW;YAC9B,SAAS,MAAM,OAAO;YACtB,WAAW,MAAM,SAAS;QAC5B;QAEA,mDAAmD;QACnD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK;YAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK;QACrB;IACF;IAEA;;GAEC,GACD,AAAQ,eAAe,KAAmB,EAAQ;QAChD,MAAM,eAAe;YACnB,UAAU,MAAM,QAAQ,KAAK,UAAU,OAAO;QAChD;QAEA,OAAQ,MAAM,QAAQ;YACpB,KAAK;gBACH,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,WAAW,EAAE;gBAC/B;YACF,KAAK;gBACH,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,MAAM,WAAW,EAAE;gBACjC;YACF,KAAK;gBACH,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,MAAM,WAAW,EAAE;gBAC9B;QACJ;IACF;IAEA;;GAEC,GACD,cAA8B;QAC5B,OAAO;eAAI,IAAI,CAAC,QAAQ;SAAC;IAC3B;IAEA;;GAEC,GACD,gBAAsB;QACpB,IAAI,CAAC,QAAQ,GAAG,EAAE;IACpB;AACF;AAGO,MAAM,sBAAsB,oBAAoB,WAAW;AAE3D,MAAM,iBAAiB,CAAC,OAAY,UACzC,oBAAoB,cAAc,CAAC,OAAO;AAErC,MAAM,qBAAqB,CAAC,OAAY,UAC7C,oBAAoB,kBAAkB,CAAC,OAAO;AAEzC,MAAM,wBAAwB,CAAC,QAAe,UACnD,oBAAoB,qBAAqB,CAAC,QAAQ;AAE7C,MAAM,cAAc,CAAC,SAAiB,cAC3C,oBAAoB,WAAW,CAAC,SAAS;AAEpC,MAAM,cAAc,CAAC,SAAiB,cAC3C,oBAAoB,WAAW,CAAC,SAAS;AAEpC,MAAM,WAAW,CAAC,SAAiB,cACxC,oBAAoB,QAAQ,CAAC,SAAS;AAGjC,MAAM,oBAAoB,CAC/B,IACA;IAEA,OAAQ,OAAO,GAAG;QAChB,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,eAAe,OAAO;gBAAE;YAAQ;YAChC,MAAM;QACR;IACF;AACF;AAGO,MAAM,mBAAmB,OAC9B,IACA,aAAqB,CAAC,EACtB,YAAoB,IAAI,EACxB;IAEA,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY;YAEZ,IAAI,YAAY,YAAY;gBAC1B,eAAe,OAAO;oBACpB,SAAS,WAAW;oBACpB,iBAAiB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAC3C;gBACA,MAAM;YACR;YAEA,4BAA4B;YAC5B,MAAM,QAAQ,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU;YAChD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,MAAM;AACR", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/api/billing.ts"], "sourcesContent": ["// Billing API client functions for medical clinic system\n// Handles bills, payments, and financial operations with comprehensive error handling\n\nimport { Bill, Payment, BillItem, PayloadResponse } from '@/types/clinic';\nimport {\n  handleAPIError,\n  handleNetworkError,\n  retryWithBackoff,\n  BillingError\n} from '@/lib/billing-error-handler';\n\n// API base URL - using relative paths for Next.js API routes\nconst API_BASE = '/api';\n\n// Enhanced error handling utility\nexport class BillingAPIError extends Error {\n  constructor(\n    message: string,\n    public status?: number,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'BillingAPIError';\n  }\n}\n\n// Enhanced API request handler with comprehensive error handling and retry logic\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {},\n  retryOptions?: { maxRetries?: number; context?: string }\n): Promise<T> {\n  const url = `${API_BASE}${endpoint}`;\n\n  const defaultHeaders = {\n    'Content-Type': 'application/json',\n  };\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      ...defaultHeaders,\n      ...options.headers,\n    },\n  };\n\n  const makeRequest = async (): Promise<T> => {\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        let errorData;\n        try {\n          errorData = await response.json();\n        } catch {\n          errorData = {\n            error: `HTTP ${response.status}: ${response.statusText}`,\n            code: `HTTP_${response.status}`,\n            message: response.statusText\n          };\n        }\n\n        const apiError = new BillingAPIError(\n          errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData.code || `HTTP_${response.status}`,\n          errorData.details\n        );\n\n        // Handle the error through the error handler\n        handleAPIError(errorData, {\n          context: retryOptions?.context || 'API Request',\n          showToast: false // Don't show toast here, let the calling function decide\n        });\n\n        throw apiError;\n      }\n\n      return await response.json();\n    } catch (error) {\n      if (error instanceof BillingAPIError) {\n        throw error;\n      }\n\n      // Handle network errors\n      const networkError = new BillingAPIError(\n        error instanceof Error ? error.message : 'Network error occurred',\n        0,\n        'NETWORK_ERROR',\n        error\n      );\n\n      handleNetworkError(error, {\n        context: retryOptions?.context || 'API Request',\n        showToast: false\n      });\n\n      throw networkError;\n    }\n  };\n\n  // Use retry logic for GET requests and other idempotent operations\n  const isIdempotent = !options.method || ['GET', 'HEAD', 'OPTIONS'].includes(options.method.toUpperCase());\n\n  if (isIdempotent && retryOptions?.maxRetries) {\n    return retryWithBackoff(\n      makeRequest,\n      retryOptions.maxRetries,\n      1000,\n      retryOptions.context\n    );\n  }\n\n  return makeRequest();\n}\n\n// Bill API Functions\nexport const billsAPI = {\n  /**\n   * Fetch all bills with optional filtering and pagination\n   */\n  async fetchBills(params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n    status?: string;\n    patientId?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Bill>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n    if (params?.status) searchParams.append('status', params.status);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/bills${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Bill>>(\n      endpoint,\n      {},\n      { maxRetries: 3, context: 'Fetch Bills' }\n    );\n  },\n\n  /**\n   * Fetch a specific bill by ID\n   */\n  async fetchBill(id: string): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`);\n  },\n\n  /**\n   * Create a new bill\n   */\n  async createBill(billData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    billType: 'treatment' | 'consultation' | 'deposit' | 'additional';\n    subtotal: number;\n    discountAmount?: number;\n    taxAmount?: number;\n    totalAmount: number;\n    description: string;\n    notes?: string;\n    dueDate: string;\n    items?: Array<{\n      itemType: 'treatment' | 'consultation' | 'material' | 'service';\n      itemName: string;\n      description?: string;\n      quantity: number;\n      unitPrice: number;\n      discountRate?: number;\n    }>;\n  }): Promise<Bill> {\n    return apiRequest<Bill>('/bills', {\n      method: 'POST',\n      body: JSON.stringify(billData),\n    });\n  },\n\n  /**\n   * Update an existing bill\n   */\n  async updateBill(id: string, updateData: Partial<Bill>): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Delete a bill\n   */\n  async deleteBill(id: string): Promise<void> {\n    return apiRequest<void>(`/bills/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  /**\n   * Generate bill from appointment\n   */\n  async generateFromAppointment(appointmentId: string, billType: string = 'treatment'): Promise<Bill> {\n    return apiRequest<Bill>('/bills/generate-from-appointment', {\n      method: 'POST',\n      body: JSON.stringify({ appointmentId, billType }),\n    });\n  },\n\n  /**\n   * Check if appointment already has a bill\n   */\n  async checkAppointmentBill(appointmentId: string): Promise<{ hasBill: boolean; bill?: Bill }> {\n    try {\n      const response = await billsAPI.fetchBills({\n        limit: 1,\n        // Note: This would need backend support for filtering by appointment\n        // For now, we'll fetch and filter client-side in components\n      });\n\n      const bill = response.docs.find(bill =>\n        typeof bill.appointment === 'object' && bill.appointment?.id === appointmentId\n      );\n\n      return {\n        hasBill: !!bill,\n        bill: bill || undefined,\n      };\n    } catch (error) {\n      console.error('Failed to check appointment bill:', error);\n      return { hasBill: false };\n    }\n  },\n};\n\n// Payment API Functions\nexport const paymentsAPI = {\n  /**\n   * Fetch all payments with optional filtering\n   */\n  async fetchPayments(params?: {\n    page?: number;\n    limit?: number;\n    billId?: string;\n    patientId?: string;\n    paymentMethod?: string;\n    status?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Payment>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.billId) searchParams.append('bill', params.billId);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.paymentMethod) searchParams.append('paymentMethod', params.paymentMethod);\n    if (params?.status) searchParams.append('paymentStatus', params.status);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/payments${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Payment>>(endpoint);\n  },\n\n  /**\n   * Fetch a specific payment by ID\n   */\n  async fetchPayment(id: string): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`);\n  },\n\n  /**\n   * Process a new payment\n   */\n  async processPayment(paymentData: {\n    bill: string;\n    patient: string;\n    amount: number;\n    paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';\n    transactionId?: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>('/payments', {\n      method: 'POST',\n      body: JSON.stringify(paymentData),\n    });\n  },\n\n  /**\n   * Update payment status\n   */\n  async updatePayment(id: string, updateData: Partial<Payment>): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Process refund\n   */\n  async processRefund(paymentId: string, refundData: {\n    amount: number;\n    reason: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${paymentId}/refund`, {\n      method: 'POST',\n      body: JSON.stringify(refundData),\n    });\n  },\n};\n\n// Financial reporting API functions\nexport const reportsAPI = {\n  /**\n   * Get daily revenue report\n   */\n  async getDailyRevenue(date: string): Promise<{\n    date: string;\n    totalRevenue: number;\n    paymentCount: number;\n    paymentMethods: Record<string, { amount: number; count: number }>;\n  }> {\n    return apiRequest(`/reports/daily-revenue?date=${date}`);\n  },\n\n  /**\n   * Get monthly revenue report\n   */\n  async getMonthlyRevenue(year: number, month: number): Promise<{\n    year: number;\n    month: number;\n    totalRevenue: number;\n    dailyBreakdown: Array<{ date: string; revenue: number }>;\n  }> {\n    return apiRequest(`/reports/monthly-revenue?year=${year}&month=${month}`);\n  },\n\n  /**\n   * Get outstanding balances report\n   */\n  async getOutstandingBalances(): Promise<{\n    totalOutstanding: number;\n    overdueAmount: number;\n    billsCount: number;\n    overdueBillsCount: number;\n    bills: Array<{\n      id: string;\n      billNumber: string;\n      patient: string;\n      amount: number;\n      dueDate: string;\n      daysOverdue: number;\n    }>;\n  }> {\n    return apiRequest('/reports/outstanding-balances');\n  },\n\n  /**\n   * Generate financial report\n   */\n  async getFinancialReport(params?: {\n    startDate?: string;\n    endDate?: string;\n    type?: 'summary' | 'detailed';\n  }): Promise<any> {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    if (params?.type) searchParams.set('type', params.type);\n\n    return apiRequest(`/reports/financial?${searchParams.toString()}`);\n  },\n};\n\n// Deposit API functions\nexport const depositsAPI = {\n  /**\n   * Create a new deposit\n   */\n  async createDeposit(depositData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    depositType: 'treatment' | 'appointment' | 'material';\n    amount: number;\n    purpose: string;\n    notes?: string;\n    expiryDate?: string;\n  }): Promise<any> {\n    return apiRequest('/deposits', {\n      method: 'POST',\n      body: JSON.stringify(depositData),\n    });\n  },\n\n  /**\n   * Get deposits with filtering and pagination\n   */\n  async getDeposits(params?: {\n    page?: number;\n    limit?: number;\n    patient?: string;\n    status?: string;\n    depositType?: string;\n  }): Promise<PayloadResponse<any>> {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n    if (params?.patient) searchParams.set('where[patient][equals]', params.patient);\n    if (params?.status) searchParams.set('where[status][equals]', params.status);\n    if (params?.depositType) searchParams.set('where[depositType][equals]', params.depositType);\n\n    return apiRequest(`/deposits?${searchParams.toString()}`);\n  },\n\n  /**\n   * Get deposit by ID\n   */\n  async getDepositById(id: string): Promise<any> {\n    return apiRequest(`/deposits/${id}`);\n  },\n\n  /**\n   * Update deposit\n   */\n  async updateDeposit(id: string, updateData: {\n    status?: string;\n    usedAmount?: number;\n    notes?: string;\n  }): Promise<any> {\n    return apiRequest(`/deposits/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Apply deposit to bill\n   */\n  async applyToBill(depositId: string, billId: string, amount: number): Promise<any> {\n    return apiRequest('/deposits/apply-to-bill', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        billId,\n        amount,\n      }),\n    });\n  },\n\n  /**\n   * Process deposit refund\n   */\n  async processRefund(depositId: string, refundAmount: number, refundReason: string, refundMethod: string = 'cash'): Promise<any> {\n    return apiRequest('/deposits/refund', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        refundAmount,\n        refundReason,\n        refundMethod,\n      }),\n    });\n  },\n};\n\n// Receipt API functions\nexport const receiptsAPI = {\n  /**\n   * Generate receipt for payment\n   */\n  async generateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`);\n  },\n\n  /**\n   * Regenerate receipt number (admin only)\n   */\n  async regenerateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`, {\n      method: 'POST',\n    });\n  },\n};\n\n\n\n// Export the error class for use in components\nexport { BillingAPIError };\n\n// Export utility functions\nexport const billingUtils = {\n  /**\n   * Format currency amount for display\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  },\n\n  /**\n   * Calculate bill total with discounts and taxes\n   */\n  calculateBillTotal(subtotal: number, discountAmount: number = 0, taxAmount: number = 0): number {\n    return subtotal + taxAmount - discountAmount;\n  },\n\n  /**\n   * Get payment method display name\n   */\n  getPaymentMethodName(method: string): string {\n    const methods: Record<string, string> = {\n      cash: '现金',\n      card: '银行卡',\n      wechat: '微信支付',\n      alipay: '支付宝',\n      transfer: '银行转账',\n      deposit: '押金抵扣',\n      installment: '分期付款',\n    };\n    return methods[method] || method;\n  },\n\n  /**\n   * Get bill status display name\n   */\n  getBillStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      draft: '草稿',\n      sent: '已发送',\n      confirmed: '已确认',\n      paid: '已支付',\n      cancelled: '已取消',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get payment status display name\n   */\n  getPaymentStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      pending: '待处理',\n      completed: '已完成',\n      failed: '失败',\n      refunded: '已退款',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get deposit status display name\n   */\n  getDepositStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      active: '有效',\n      used: '已使用',\n      refunded: '已退还',\n      expired: '已过期',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Validate payment amount against bill balance\n   */\n  validatePaymentAmount(amount: number, billBalance: number): boolean {\n    return amount > 0 && amount <= billBalance;\n  },\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,sFAAsF;;;;;;;;;;AAGtF;;AAOA,6DAA6D;AAC7D,MAAM,WAAW;AAGV,MAAM,wBAAwB;;;;IACnC,YACE,OAAe,EACf,AAAO,MAAe,EACtB,AAAO,IAAa,EACpB,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eAJC,SAAA,aACA,OAAA,WACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,iFAAiF;AACjF,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACzB,YAAwD;IAExD,MAAM,MAAM,GAAG,WAAW,UAAU;IAEpC,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,SAAsB;QAC1B,GAAG,OAAO;QACV,SAAS;YACP,GAAG,cAAc;YACjB,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,IAAI;gBACJ,IAAI;oBACF,YAAY,MAAM,SAAS,IAAI;gBACjC,EAAE,OAAM;oBACN,YAAY;wBACV,OAAO,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;wBACxD,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;wBAC/B,SAAS,SAAS,UAAU;oBAC9B;gBACF;gBAEA,MAAM,WAAW,IAAI,gBACnB,UAAU,KAAK,IAAI,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACzF,SAAS,MAAM,EACf,UAAU,IAAI,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE,EAC3C,UAAU,OAAO;gBAGnB,6CAA6C;gBAC7C,CAAA,GAAA,yIAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;oBACxB,SAAS,cAAc,WAAW;oBAClC,WAAW,MAAM,yDAAyD;gBAC5E;gBAEA,MAAM;YACR;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,iBAAiB;gBACpC,MAAM;YACR;YAEA,wBAAwB;YACxB,MAAM,eAAe,IAAI,gBACvB,iBAAiB,QAAQ,MAAM,OAAO,GAAG,0BACzC,GACA,iBACA;YAGF,CAAA,GAAA,yIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;gBACxB,SAAS,cAAc,WAAW;gBAClC,WAAW;YACb;YAEA,MAAM;QACR;IACF;IAEA,mEAAmE;IACnE,MAAM,eAAe,CAAC,QAAQ,MAAM,IAAI;QAAC;QAAO;QAAQ;KAAU,CAAC,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW;IAEtG,IAAI,gBAAgB,cAAc,YAAY;QAC5C,OAAO,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD,EACpB,aACA,aAAa,UAAU,EACvB,MACA,aAAa,OAAO;IAExB;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB;;GAEC,GACD,MAAM,YAAW,MAQhB;QACC,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAC/D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,WAAW,OAAO,SAAS;QACtE,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,WAAW,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEhE,OAAO,WACL,UACA,CAAC,GACD;YAAE,YAAY;YAAG,SAAS;QAAc;IAE5C;IAEA;;GAEC,GACD,MAAM,WAAU,EAAU;QACxB,OAAO,WAAiB,CAAC,OAAO,EAAE,IAAI;IACxC;IAEA;;GAEC,GACD,MAAM,YAAW,QAoBhB;QACC,OAAO,WAAiB,UAAU;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,YAAW,EAAU,EAAE,UAAyB;QACpD,OAAO,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,YAAW,EAAU;QACzB,OAAO,WAAiB,CAAC,OAAO,EAAE,IAAI,EAAE;YACtC,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,MAAM,yBAAwB,aAAqB,EAAE,WAAmB,WAAW;QACjF,OAAO,WAAiB,oCAAoC;YAC1D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAe;YAAS;QACjD;IACF;IAEA;;GAEC,GACD,MAAM,sBAAqB,aAAqB;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,SAAS,UAAU,CAAC;gBACzC,OAAO;YAGT;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,OAC9B,OAAO,KAAK,WAAW,KAAK,YAAY,KAAK,WAAW,EAAE,OAAO;YAGnE,OAAO;gBACL,SAAS,CAAC,CAAC;gBACX,MAAM,QAAQ;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,SAAS;YAAM;QAC1B;IACF;AACF;AAGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,eAAc,MASnB;QACC,MAAM,eAAe,IAAI;QAEzB,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,QAAQ,OAAO,MAAM;QAC7D,IAAI,QAAQ,WAAW,aAAa,MAAM,CAAC,WAAW,OAAO,SAAS;QACtE,IAAI,QAAQ,eAAe,aAAa,MAAM,CAAC,iBAAiB,OAAO,aAAa;QACpF,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACtE,IAAI,QAAQ,UAAU,aAAa,MAAM,CAAC,YAAY,OAAO,QAAQ;QACrE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,UAAU,OAAO,MAAM;QAE/D,MAAM,cAAc,aAAa,QAAQ;QACzC,MAAM,WAAW,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEnE,OAAO,WAAqC;IAC9C;IAEA;;GAEC,GACD,MAAM,cAAa,EAAU;QAC3B,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI;IAC9C;IAEA;;GAEC,GACD,MAAM,gBAAe,WAOpB;QACC,OAAO,WAAoB,aAAa;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,eAAc,EAAU,EAAE,UAA4B;QAC1D,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,eAAc,SAAiB,EAAE,UAItC;QACC,OAAO,WAAoB,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC,EAAE;YAC1D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,aAAa;IACxB;;GAEC,GACD,MAAM,iBAAgB,IAAY;QAMhC,OAAO,WAAW,CAAC,4BAA4B,EAAE,MAAM;IACzD;IAEA;;GAEC,GACD,MAAM,mBAAkB,IAAY,EAAE,KAAa;QAMjD,OAAO,WAAW,CAAC,8BAA8B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC1E;IAEA;;GAEC,GACD,MAAM;QAcJ,OAAO,WAAW;IACpB;IAEA;;GAEC,GACD,MAAM,oBAAmB,MAIxB;QACC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,WAAW,OAAO,OAAO;QAC/D,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI;QAEtD,OAAO,WAAW,CAAC,mBAAmB,EAAE,aAAa,QAAQ,IAAI;IACnE;AACF;AAGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,eAAc,WASnB;QACC,OAAO,WAAW,aAAa;YAC7B,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,aAAY,MAMjB;QACC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,MAAM,aAAa,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAC/D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAClE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,0BAA0B,OAAO,OAAO;QAC9E,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,yBAAyB,OAAO,MAAM;QAC3E,IAAI,QAAQ,aAAa,aAAa,GAAG,CAAC,8BAA8B,OAAO,WAAW;QAE1F,OAAO,WAAW,CAAC,UAAU,EAAE,aAAa,QAAQ,IAAI;IAC1D;IAEA;;GAEC,GACD,MAAM,gBAAe,EAAU;QAC7B,OAAO,WAAW,CAAC,UAAU,EAAE,IAAI;IACrC;IAEA;;GAEC,GACD,MAAM,eAAc,EAAU,EAAE,UAI/B;QACC,OAAO,WAAW,CAAC,UAAU,EAAE,IAAI,EAAE;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,aAAY,SAAiB,EAAE,MAAc,EAAE,MAAc;QACjE,OAAO,WAAW,2BAA2B;YAC3C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAc,SAAiB,EAAE,YAAoB,EAAE,YAAoB,EAAE,eAAuB,MAAM;QAC9G,OAAO,WAAW,oBAAoB;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA;gBACA;YACF;QACF;IACF;AACF;AAGO,MAAM,cAAc;IACzB;;GAEC,GACD,MAAM,iBAAgB,SAAiB;QACrC,OAAO,WAAW,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;IACpD;IAEA;;GAEC,GACD,MAAM,mBAAkB,SAAiB;QACvC,OAAO,WAAW,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC,EAAE;YAClD,QAAQ;QACV;IACF;AACF;;AAQO,MAAM,eAAe;IAC1B;;GAEC,GACD,gBAAe,MAAc;QAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA;;GAEC,GACD,oBAAmB,QAAgB,EAAE,iBAAyB,CAAC,EAAE,YAAoB,CAAC;QACpF,OAAO,WAAW,YAAY;IAChC;IAEA;;GAEC,GACD,sBAAqB,MAAc;QACjC,MAAM,UAAkC;YACtC,MAAM;YACN,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,SAAS;YACT,aAAa;QACf;QACA,OAAO,OAAO,CAAC,OAAO,IAAI;IAC5B;IAEA;;GAEC,GACD,mBAAkB,MAAc;QAC9B,MAAM,WAAmC;YACvC,OAAO;YACP,MAAM;YACN,WAAW;YACX,MAAM;YACN,WAAW;QACb;QACA,OAAO,QAAQ,CAAC,OAAO,IAAI;IAC7B;IAEA;;GAEC,GACD,sBAAqB,MAAc;QACjC,MAAM,WAAmC;YACvC,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;QACZ;QACA,OAAO,QAAQ,CAAC,OAAO,IAAI;IAC7B;IAEA;;GAEC,GACD,sBAAqB,MAAc;QACjC,MAAM,WAAmC;YACvC,QAAQ;YACR,MAAM;YACN,UAAU;YACV,SAAS;QACX;QACA,OAAO,QAAQ,CAAC,OAAO,IAAI;IAC7B;IAEA;;GAEC,GACD,uBAAsB,MAAc,EAAE,WAAmB;QACvD,OAAO,SAAS,KAAK,UAAU;IACjC;AACF", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot='dialog' {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} />;\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} />;\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot='dialog-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot='dialog-portal'>\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot='dialog-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-header'\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-footer'\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot='dialog-title'\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot='dialog-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6VAAC,+QAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6VAAC,+QAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6VAAC,+QAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6VAAC,+QAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6VAAC,+QAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6VAAC;QAAa,aAAU;;0BACtB,6VAAC;;;;;0BACD,6VAAC,+QAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6VAAC,+QAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6VAAC,oRAAA,CAAA,QAAK;;;;;0CACN,6VAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6VAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6VAAC,+QAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/validation/billing-schemas.ts"], "sourcesContent": ["// Comprehensive validation schemas for billing forms\n// Provides robust client-side validation with detailed error messages in Chinese\n\nimport * as z from 'zod';\n\n// Common validation patterns\nconst positiveNumber = z.number().min(0, '金额不能为负数');\nconst requiredString = z.string().min(1, '此字段为必填项');\nconst optionalString = z.string();\nconst phoneRegex = /^1[3-9]\\d{9}$/;\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\n// Custom validation functions\nconst validateCurrency = (value: number) => {\n  if (value < 0) return false;\n  // Check for reasonable decimal places (max 2)\n  const decimalPlaces = (value.toString().split('.')[1] || '').length;\n  return decimalPlaces <= 2;\n};\n\nconst validateDateNotInPast = (date: string) => {\n  const inputDate = new Date(date);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return inputDate >= today;\n};\n\nconst validateDateNotTooFarInFuture = (date: string) => {\n  const inputDate = new Date(date);\n  const maxDate = new Date();\n  maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future\n  return inputDate <= maxDate;\n};\n\n// Bill Item validation schema\nexport const billItemSchema = z.object({\n  itemType: z.enum(['treatment', 'consultation', 'material', 'service'], {\n    required_error: '请选择项目类型',\n    invalid_type_error: '无效的项目类型',\n  }),\n  itemName: requiredString.max(100, '项目名称不能超过100个字符'),\n  description: optionalString.max(500, '描述不能超过500个字符').optional(),\n  quantity: z.number()\n    .min(0.01, '数量必须大于0')\n    .max(9999, '数量不能超过9999')\n    .refine((val) => {\n      const decimalPlaces = (val.toString().split('.')[1] || '').length;\n      return decimalPlaces <= 3;\n    }, '数量最多支持3位小数'),\n  unitPrice: z.number()\n    .min(0, '单价不能为负数')\n    .max(999999.99, '单价不能超过999,999.99')\n    .refine(validateCurrency, '单价格式无效，最多支持2位小数'),\n  discountRate: z.number()\n    .min(0, '折扣率不能为负数')\n    .max(100, '折扣率不能超过100%')\n    .optional(),\n}).refine((data) => {\n  // Validate that discount rate makes sense\n  if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {\n    return false;\n  }\n  return true;\n}, {\n  message: '单价为0时不能设置折扣',\n  path: ['discountRate'],\n});\n\n// Bill form validation schema\nexport const billFormSchema = z.object({\n  patient: requiredString.uuid('请选择有效的患者'),\n  appointment: optionalString.uuid('请选择有效的预约').optional().or(z.literal('')),\n  treatment: optionalString.uuid('请选择有效的治疗项目').optional().or(z.literal('')),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional'], {\n    required_error: '请选择账单类型',\n    invalid_type_error: '无效的账单类型',\n  }),\n  description: requiredString\n    .min(2, '账单描述至少需要2个字符')\n    .max(200, '账单描述不能超过200个字符'),\n  notes: optionalString.max(1000, '备注不能超过1000个字符').optional(),\n  dueDate: z.string()\n    .min(1, '请选择到期日期')\n    .refine((date) => {\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的日期')\n    .refine(validateDateNotInPast, '到期日期不能是过去的日期')\n    .refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),\n  discountAmount: z.number()\n    .min(0, '折扣金额不能为负数')\n    .max(999999.99, '折扣金额不能超过999,999.99')\n    .refine(validateCurrency, '折扣金额格式无效')\n    .optional(),\n  taxAmount: z.number()\n    .min(0, '税费金额不能为负数')\n    .max(999999.99, '税费金额不能超过999,999.99')\n    .refine(validateCurrency, '税费金额格式无效')\n    .optional(),\n  items: z.array(billItemSchema)\n    .min(1, '至少需要一个账单项目')\n    .max(50, '账单项目不能超过50个'),\n}).refine((data) => {\n  // Validate that bill has reasonable total\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  const taxAmount = data.taxAmount || 0;\n  const finalTotal = itemsTotal + taxAmount - discountAmount;\n  \n  return finalTotal >= 0;\n}, {\n  message: '账单总金额不能为负数',\n  path: ['discountAmount'],\n}).refine((data) => {\n  // Validate discount doesn't exceed subtotal\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  return discountAmount <= itemsTotal;\n}, {\n  message: '折扣金额不能超过项目小计',\n  path: ['discountAmount'],\n});\n\n// Payment form validation schema\nexport const paymentFormSchema = z.object({\n  amount: z.number()\n    .min(0.01, '支付金额必须大于0')\n    .max(999999.99, '支付金额不能超过999,999.99')\n    .refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),\n  paymentMethod: z.enum(['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'], {\n    required_error: '请选择支付方式',\n    invalid_type_error: '无效的支付方式',\n  }),\n  transactionId: z.string()\n    .max(100, '交易ID不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  notes: optionalString.max(500, '备注不能超过500个字符').optional(),\n}).refine((data) => {\n  // Require transaction ID for certain payment methods\n  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];\n  if (methodsRequiringTransactionId.includes(data.paymentMethod)) {\n    return data.transactionId && data.transactionId.trim().length > 0;\n  }\n  return true;\n}, {\n  message: '此支付方式需要提供交易ID',\n  path: ['transactionId'],\n});\n\n// Patient form validation schema (for billing context)\nexport const patientFormSchema = z.object({\n  fullName: requiredString\n    .min(2, '姓名至少需要2个字符')\n    .max(50, '姓名不能超过50个字符')\n    .regex(/^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/, '姓名只能包含中文、英文和空格'),\n  phone: requiredString\n    .regex(phoneRegex, '请输入有效的手机号码'),\n  email: z.string()\n    .email('请输入有效的邮箱地址')\n    .max(100, '邮箱地址不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符').optional(),\n});\n\n// Bill status update validation schema\nexport const billStatusUpdateSchema = z.object({\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled'], {\n    required_error: '请选择账单状态',\n    invalid_type_error: '无效的账单状态',\n  }),\n  notes: optionalString.max(500, '状态更新备注不能超过500个字符').optional(),\n});\n\n// Filter validation schema\nexport const billFilterSchema = z.object({\n  search: optionalString.max(100, '搜索关键词不能超过100个字符').optional(),\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled']).optional(),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional']).optional(),\n  patientId: optionalString.uuid('请选择有效的患者').optional().or(z.literal('')),\n  dateFrom: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的开始日期'),\n  dateTo: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的结束日期'),\n  amountMin: z.number()\n    .min(0, '最小金额不能为负数')\n    .max(999999.99, '最小金额不能超过999,999.99')\n    .optional(),\n  amountMax: z.number()\n    .min(0, '最大金额不能为负数')\n    .max(999999.99, '最大金额不能超过999,999.99')\n    .optional(),\n}).refine((data) => {\n  // Validate date range\n  if (data.dateFrom && data.dateTo) {\n    const fromDate = new Date(data.dateFrom);\n    const toDate = new Date(data.dateTo);\n    return fromDate <= toDate;\n  }\n  return true;\n}, {\n  message: '开始日期不能晚于结束日期',\n  path: ['dateTo'],\n}).refine((data) => {\n  // Validate amount range\n  if (data.amountMin !== undefined && data.amountMax !== undefined) {\n    return data.amountMin <= data.amountMax;\n  }\n  return true;\n}, {\n  message: '最小金额不能大于最大金额',\n  path: ['amountMax'],\n});\n\n// Export types for TypeScript\nexport type BillItemFormData = z.infer<typeof billItemSchema>;\nexport type BillFormData = z.infer<typeof billFormSchema>;\nexport type PaymentFormData = z.infer<typeof paymentFormSchema>;\nexport type PatientFormData = z.infer<typeof patientFormSchema>;\nexport type BillStatusUpdateData = z.infer<typeof billStatusUpdateSchema>;\nexport type BillFilterData = z.infer<typeof billFilterSchema>;\n\n// Validation helper functions\nexport const validateBillItem = (data: unknown) => {\n  return billItemSchema.safeParse(data);\n};\n\nexport const validateBillForm = (data: unknown) => {\n  return billFormSchema.safeParse(data);\n};\n\nexport const validatePaymentForm = (data: unknown) => {\n  return paymentFormSchema.safeParse(data);\n};\n\nexport const validatePatientForm = (data: unknown) => {\n  return patientFormSchema.safeParse(data);\n};\n\nexport const validateBillStatusUpdate = (data: unknown) => {\n  return billStatusUpdateSchema.safeParse(data);\n};\n\nexport const validateBillFilter = (data: unknown) => {\n  return billFilterSchema.safeParse(data);\n};\n\n// Custom validation error formatter\nexport const formatValidationErrors = (errors: z.ZodError) => {\n  return errors.errors.map(error => ({\n    field: error.path.join('.'),\n    message: error.message,\n    code: error.code,\n  }));\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,iFAAiF;;;;;;;;;;;;;;;;AAEjF;;AAEA,6BAA6B;AAC7B,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AACzC,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;AACzC,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD;AAC9B,MAAM,aAAa;AACnB,MAAM,aAAa;AAEnB,8BAA8B;AAC9B,MAAM,mBAAmB,CAAC;IACxB,IAAI,QAAQ,GAAG,OAAO;IACtB,8CAA8C;IAC9C,MAAM,gBAAgB,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;IACnE,OAAO,iBAAiB;AAC1B;AAEA,MAAM,wBAAwB,CAAC;IAC7B,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IACxB,OAAO,aAAa;AACtB;AAEA,MAAM,gCAAgC,CAAC;IACrC,MAAM,YAAY,IAAI,KAAK;IAC3B,MAAM,UAAU,IAAI;IACpB,QAAQ,WAAW,CAAC,QAAQ,WAAW,KAAK,IAAI,wBAAwB;IACxE,OAAO,aAAa;AACtB;AAGO,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,EAAE;IACrC,UAAU,CAAA,GAAA,qLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAY;KAAU,EAAE;QACrE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,UAAU,eAAe,GAAG,CAAC,KAAK;IAClC,aAAa,eAAe,GAAG,CAAC,KAAK,gBAAgB,QAAQ;IAC7D,UAAU,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACd,GAAG,CAAC,MAAM,WACV,GAAG,CAAC,MAAM,cACV,MAAM,CAAC,CAAC;QACP,MAAM,gBAAgB,CAAC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;QACjE,OAAO,iBAAiB;IAC1B,GAAG;IACL,WAAW,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,WACP,GAAG,CAAC,WAAW,oBACf,MAAM,CAAC,kBAAkB;IAC5B,cAAc,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IAClB,GAAG,CAAC,GAAG,YACP,GAAG,CAAC,KAAK,eACT,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC;IACT,0CAA0C;IAC1C,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,KAAK,KAAK,SAAS,KAAK,GAAG;QACtE,OAAO;IACT;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAe;AACxB;AAGO,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,EAAE;IACrC,SAAS,eAAe,IAAI,CAAC;IAC7B,aAAa,eAAe,IAAI,CAAC,YAAY,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,qLAAA,CAAA,UAAS,AAAD,EAAE;IACrE,WAAW,eAAe,IAAI,CAAC,cAAc,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,qLAAA,CAAA,UAAS,AAAD,EAAE;IACrE,UAAU,CAAA,GAAA,qLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAW;KAAa,EAAE;QACvE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,aAAa,eACV,GAAG,CAAC,GAAG,gBACP,GAAG,CAAC,KAAK;IACZ,OAAO,eAAe,GAAG,CAAC,MAAM,iBAAiB,QAAQ;IACzD,SAAS,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACb,GAAG,CAAC,GAAG,WACP,MAAM,CAAC,CAAC;QACP,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG,YACF,MAAM,CAAC,uBAAuB,gBAC9B,MAAM,CAAC,+BAA+B;IACzC,gBAAgB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACpB,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB,YACzB,QAAQ;IACX,WAAW,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB,YACzB,QAAQ;IACX,OAAO,CAAA,GAAA,qLAAA,CAAA,QAAO,AAAD,EAAE,gBACZ,GAAG,CAAC,GAAG,cACP,GAAG,CAAC,IAAI;AACb,GAAG,MAAM,CAAC,CAAC;IACT,0CAA0C;IAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;QAChD,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;QAChE,OAAO,MAAM,CAAC,YAAY,YAAY;IACxC,GAAG;IAEH,MAAM,iBAAiB,KAAK,cAAc,IAAI;IAC9C,MAAM,YAAY,KAAK,SAAS,IAAI;IACpC,MAAM,aAAa,aAAa,YAAY;IAE5C,OAAO,cAAc;AACvB,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAiB;AAC1B,GAAG,MAAM,CAAC,CAAC;IACT,4CAA4C;IAC5C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,SAAS;QAChD,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;QAChE,OAAO,MAAM,CAAC,YAAY,YAAY;IACxC,GAAG;IAEH,MAAM,iBAAiB,KAAK,cAAc,IAAI;IAC9C,OAAO,kBAAkB;AAC3B,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAiB;AAC1B;AAGO,MAAM,oBAAoB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,EAAE;IACxC,QAAQ,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACZ,GAAG,CAAC,MAAM,aACV,GAAG,CAAC,WAAW,sBACf,MAAM,CAAC,kBAAkB;IAC5B,eAAe,CAAA,GAAA,qLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAQ;QAAQ;QAAU;QAAU;QAAY;KAAc,EAAE;QACrF,gBAAgB;QAChB,oBAAoB;IACtB;IACA,eAAe,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACnB,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,qLAAA,CAAA,UAAS,AAAD,EAAE;IAChB,OAAO,eAAe,GAAG,CAAC,KAAK,gBAAgB,QAAQ;AACzD,GAAG,MAAM,CAAC,CAAC;IACT,qDAAqD;IACrD,MAAM,gCAAgC;QAAC;QAAQ;QAAU;QAAU;KAAW;IAC9E,IAAI,8BAA8B,QAAQ,CAAC,KAAK,aAAa,GAAG;QAC9D,OAAO,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,IAAI,GAAG,MAAM,GAAG;IAClE;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAgB;AACzB;AAGO,MAAM,oBAAoB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,EAAE;IACxC,UAAU,eACP,GAAG,CAAC,GAAG,cACP,GAAG,CAAC,IAAI,eACR,KAAK,CAAC,8BAA8B;IACvC,OAAO,eACJ,KAAK,CAAC,YAAY;IACrB,OAAO,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACX,KAAK,CAAC,cACN,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,qLAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,eAAe,GAAG,CAAC,MAAM,mBAAmB,QAAQ;AACpE;AAGO,MAAM,yBAAyB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,EAAE;IAC7C,QAAQ,CAAA,GAAA,qLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;QAAQ;QAAa;QAAQ;KAAY,EAAE;QAClE,gBAAgB;QAChB,oBAAoB;IACtB;IACA,OAAO,eAAe,GAAG,CAAC,KAAK,oBAAoB,QAAQ;AAC7D;AAGO,MAAM,mBAAmB,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,EAAE;IACvC,QAAQ,eAAe,GAAG,CAAC,KAAK,mBAAmB,QAAQ;IAC3D,QAAQ,CAAA,GAAA,qLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;QAAQ;QAAa;QAAQ;KAAY,EAAE,QAAQ;IAC5E,UAAU,CAAA,GAAA,qLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAgB;QAAW;KAAa,EAAE,QAAQ;IACjF,WAAW,eAAe,IAAI,CAAC,YAAY,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,qLAAA,CAAA,UAAS,AAAD,EAAE;IACnE,UAAU,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACd,QAAQ,GACR,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG;IACL,QAAQ,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACZ,QAAQ,GACR,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI;YACF,IAAI,KAAK;YACT,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF,GAAG;IACL,WAAW,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,QAAQ;IACX,WAAW,CAAA,GAAA,qLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG,aACP,GAAG,CAAC,WAAW,sBACf,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC;IACT,sBAAsB;IACtB,IAAI,KAAK,QAAQ,IAAI,KAAK,MAAM,EAAE;QAChC,MAAM,WAAW,IAAI,KAAK,KAAK,QAAQ;QACvC,MAAM,SAAS,IAAI,KAAK,KAAK,MAAM;QACnC,OAAO,YAAY;IACrB;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAS;AAClB,GAAG,MAAM,CAAC,CAAC;IACT,wBAAwB;IACxB,IAAI,KAAK,SAAS,KAAK,aAAa,KAAK,SAAS,KAAK,WAAW;QAChE,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS;IACzC;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAY;AACrB;AAWO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,SAAS,CAAC;AAClC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,eAAe,SAAS,CAAC;AAClC;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,SAAS,CAAC;AACrC;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,kBAAkB,SAAS,CAAC;AACrC;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,uBAAuB,SAAS,CAAC;AAC1C;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,iBAAiB,SAAS,CAAC;AACpC;AAGO,MAAM,yBAAyB,CAAC;IACrC,OAAO,OAAO,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;YACjC,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC;YACvB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;QAClB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/billing-notifications.ts"], "sourcesContent": ["// Comprehensive toast notification utilities for billing actions\n// Provides consistent messaging in Chinese for all billing operations\n\nimport { toast } from 'sonner';\nimport { Bill, Payment, BillItem } from '@/types/clinic';\nimport { billingUtils } from './api/billing';\n\nexport const billingNotifications = {\n  // Bill-related notifications\n  bill: {\n    created: (bill: Bill) => {\n      toast.success(`账单创建成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    updated: (bill: Bill) => {\n      toast.success(`账单更新成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    deleted: (billNumber: string) => {\n      toast.success(`账单删除成功！`, {\n        description: `账单编号: ${billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    statusUpdated: (bill: Bill, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        draft: '草稿',\n        sent: '已发送',\n        confirmed: '已确认',\n        paid: '已支付',\n        cancelled: '已取消',\n      };\n      \n      toast.success(`账单状态已更新！`, {\n        description: `${bill.billNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 5000,\n      });\n    },\n\n    generateFromAppointment: (bill: Bill, appointmentDate: string) => {\n      toast.success(`从预约生成账单成功！`, {\n        description: `预约日期: ${appointmentDate}，账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    loadError: (error?: string) => {\n      toast.error(`加载账单失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    createError: (error?: string) => {\n      toast.error(`创建账单失败`, {\n        description: error || '请检查输入信息后重试',\n        duration: 5000,\n      });\n    },\n\n    updateError: (error?: string) => {\n      toast.error(`更新账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    deleteError: (error?: string) => {\n      toast.error(`删除账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`账单验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Payment-related notifications\n  payment: {\n    processed: (payment: Payment) => {\n      toast.success(`支付处理成功！`, {\n        description: `支付金额: ${billingUtils.formatCurrency(payment.amount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    receiptGenerated: (payment: Payment) => {\n      toast.success(`收据生成成功！`, {\n        description: `收据编号: ${payment.receiptNumber || '待生成'}`,\n        duration: 4000,\n      });\n    },\n\n    refunded: (payment: Payment, refundAmount: number) => {\n      toast.success(`退款处理成功！`, {\n        description: `退款金额: ${billingUtils.formatCurrency(refundAmount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    statusUpdated: (payment: Payment, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        pending: '待处理',\n        completed: '已完成',\n        failed: '失败',\n        refunded: '已退款',\n      };\n\n      toast.success(`支付状态已更新！`, {\n        description: `${payment.paymentNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 4000,\n      });\n    },\n\n    processError: (error?: string) => {\n      toast.error(`支付处理失败`, {\n        description: error || '请检查支付信息后重试',\n        duration: 5000,\n      });\n    },\n\n    refundError: (error?: string) => {\n      toast.error(`退款处理失败`, {\n        description: error || '请联系管理员处理',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`支付验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n\n    amountExceeded: (maxAmount: number) => {\n      toast.error(`支付金额超限`, {\n        description: `最大支付金额: ${billingUtils.formatCurrency(maxAmount)}`,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Receipt-related notifications\n  receipt: {\n    printed: (receiptNumber: string) => {\n      toast.success(`收据打印成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    downloaded: (receiptNumber: string) => {\n      toast.success(`收据下载成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    printError: () => {\n      toast.error(`收据打印失败`, {\n        description: '请检查打印机设置',\n        duration: 4000,\n      });\n    },\n\n    downloadError: () => {\n      toast.error(`收据下载失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    notFound: (receiptNumber: string) => {\n      toast.error(`收据未找到`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // General system notifications\n  system: {\n    loading: (action: string) => {\n      toast.loading(`${action}中...`, {\n        duration: Infinity, // Will be dismissed manually\n      });\n    },\n\n    networkError: () => {\n      toast.error(`网络连接失败`, {\n        description: '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    permissionDenied: (action: string) => {\n      toast.error(`权限不足`, {\n        description: `您没有权限执行: ${action}`,\n        duration: 5000,\n      });\n    },\n\n    dataRefreshed: () => {\n      toast.success(`数据刷新成功`, {\n        duration: 2000,\n      });\n    },\n\n    dataRefreshError: () => {\n      toast.error(`数据刷新失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    operationCancelled: (operation: string) => {\n      toast.info(`${operation}已取消`, {\n        duration: 2000,\n      });\n    },\n\n    featureNotImplemented: (feature: string) => {\n      toast.info(`${feature}功能开发中...`, {\n        description: '敬请期待',\n        duration: 3000,\n      });\n    },\n  },\n\n  // Financial reporting notifications\n  financial: {\n    reportGenerated: (reportType: string, period: string) => {\n      toast.success(`${reportType}生成成功！`, {\n        description: `报表期间: ${period}`,\n        duration: 4000,\n      });\n    },\n\n    reportError: (reportType: string, error?: string) => {\n      toast.error(`${reportType}生成失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    dataExported: (format: string) => {\n      toast.success(`数据导出成功！`, {\n        description: `格式: ${format}`,\n        duration: 3000,\n      });\n    },\n\n    exportError: (error?: string) => {\n      toast.error(`数据导出失败`, {\n        description: error || '请稍后重试',\n        duration: 4000,\n      });\n    },\n  },\n\n  // Validation and warning notifications\n  validation: {\n    requiredField: (fieldName: string) => {\n      toast.error(`字段验证失败`, {\n        description: `${fieldName}为必填项`,\n        duration: 4000,\n      });\n    },\n\n    invalidFormat: (fieldName: string, expectedFormat: string) => {\n      toast.error(`格式验证失败`, {\n        description: `${fieldName}格式应为: ${expectedFormat}`,\n        duration: 4000,\n      });\n    },\n\n    duplicateEntry: (itemType: string, identifier: string) => {\n      toast.error(`重复条目`, {\n        description: `${itemType} \"${identifier}\" 已存在`,\n        duration: 4000,\n      });\n    },\n\n    unsavedChanges: () => {\n      toast.warning(`有未保存的更改`, {\n        description: '请保存后再继续',\n        duration: 4000,\n      });\n    },\n\n    confirmAction: (action: string) => {\n      toast.warning(`请确认操作`, {\n        description: `即将执行: ${action}`,\n        duration: 5000,\n      });\n    },\n  },\n};\n\n// Utility function to dismiss all toasts\nexport const dismissAllToasts = () => {\n  toast.dismiss();\n};\n\n// Utility function to show custom toast with consistent styling\nexport const showCustomToast = (\n  type: 'success' | 'error' | 'warning' | 'info',\n  title: string,\n  description?: string,\n  duration: number = 4000\n) => {\n  const toastFunction = toast[type];\n  toastFunction(title, {\n    description,\n    duration,\n  });\n};\n"], "names": [], "mappings": "AAAA,iEAAiE;AACjE,sEAAsE;;;;;;AAEtE;AAEA;;;AAEO,MAAM,uBAAuB;IAClC,6BAA6B;IAC7B,MAAM;QACJ,SAAS,CAAC;YACR,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,KAAK,UAAU,EAAE;gBACvC,UAAU;YACZ;QACF;QAEA,SAAS,CAAC;YACR,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,KAAK,UAAU,EAAE;gBACvC,UAAU;YACZ;QACF;QAEA,SAAS,CAAC;YACR,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,YAAY;gBAClC,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,MAAY,WAAmB;YAC7C,MAAM,cAAc;gBAClB,OAAO;gBACP,MAAM;gBACN,WAAW;gBACX,MAAM;gBACN,WAAW;YACb;YAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACxB,aAAa,GAAG,KAAK,UAAU,CAAC,EAAE,EAAE,WAAW,CAAC,UAAsC,CAAC,GAAG,EAAE,WAAW,CAAC,UAAsC,EAAE;gBAChJ,UAAU;YACZ;QACF;QAEA,yBAAyB,CAAC,MAAY;YACpC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,EAAE;gBAC1B,aAAa,CAAC,MAAM,EAAE,gBAAgB,OAAO,EAAE,KAAK,UAAU,EAAE;gBAChE,UAAU;YACZ;QACF;QAEA,WAAW,CAAC;YACV,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,iBAAiB,CAAC;YAChB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;IACF;IAEA,gCAAgC;IAChC,SAAS;QACP,WAAW,CAAC;YACV,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,MAAM,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE;gBAClG,UAAU;YACZ;QACF;QAEA,kBAAkB,CAAC;YACjB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,QAAQ,aAAa,IAAI,OAAO;gBACtD,UAAU;YACZ;QACF;QAEA,UAAU,CAAC,SAAkB;YAC3B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,cAAc,OAAO,EAAE,QAAQ,aAAa,EAAE;gBAChG,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,SAAkB,WAAmB;YACnD,MAAM,cAAc;gBAClB,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;YACZ;YAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACxB,aAAa,GAAG,QAAQ,aAAa,CAAC,EAAE,EAAE,WAAW,CAAC,UAAsC,CAAC,GAAG,EAAE,WAAW,CAAC,UAAsC,EAAE;gBACtJ,UAAU;YACZ;QACF;QAEA,cAAc,CAAC;YACb,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,iBAAiB,CAAC;YAChB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,gBAAgB,CAAC;YACf,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,CAAC,QAAQ,EAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,YAAY;gBAChE,UAAU;YACZ;QACF;IACF;IAEA,gCAAgC;IAChC,SAAS;QACP,SAAS,CAAC;YACR,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,eAAe;gBACrC,UAAU;YACZ;QACF;QAEA,YAAY,CAAC;YACX,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,MAAM,EAAE,eAAe;gBACrC,UAAU;YACZ;QACF;QAEA,YAAY;YACV,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,eAAe;YACb,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,UAAU,CAAC;YACT,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE;gBACnB,aAAa,CAAC,MAAM,EAAE,eAAe;gBACrC,UAAU;YACZ;QACF;IACF;IAEA,+BAA+B;IAC/B,QAAQ;QACN,SAAS,CAAC;YACR,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,EAAE;gBAC7B,UAAU;YACZ;QACF;QAEA,cAAc;YACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,kBAAkB,CAAC;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClB,aAAa,CAAC,SAAS,EAAE,QAAQ;gBACjC,UAAU;YACZ;QACF;QAEA,eAAe;YACb,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE;gBACtB,UAAU;YACZ;QACF;QAEA,kBAAkB;YAChB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,oBAAoB,CAAC;YACnB,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE;gBAC5B,UAAU;YACZ;QACF;QAEA,uBAAuB,CAAC;YACtB,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,QAAQ,CAAC,EAAE;gBAC/B,aAAa;gBACb,UAAU;YACZ;QACF;IACF;IAEA,oCAAoC;IACpC,WAAW;QACT,iBAAiB,CAAC,YAAoB;YACpC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,WAAW,KAAK,CAAC,EAAE;gBAClC,aAAa,CAAC,MAAM,EAAE,QAAQ;gBAC9B,UAAU;YACZ;QACF;QAEA,aAAa,CAAC,YAAoB;YAChC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,EAAE;gBAC/B,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;QAEA,cAAc,CAAC;YACb,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,IAAI,EAAE,QAAQ;gBAC5B,UAAU;YACZ;QACF;QAEA,aAAa,CAAC;YACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,SAAS;gBACtB,UAAU;YACZ;QACF;IACF;IAEA,uCAAuC;IACvC,YAAY;QACV,eAAe,CAAC;YACd,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,GAAG,UAAU,IAAI,CAAC;gBAC/B,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,WAAmB;YACjC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,GAAG,UAAU,MAAM,EAAE,gBAAgB;gBAClD,UAAU;YACZ;QACF;QAEA,gBAAgB,CAAC,UAAkB;YACjC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClB,aAAa,GAAG,SAAS,EAAE,EAAE,WAAW,KAAK,CAAC;gBAC9C,UAAU;YACZ;QACF;QAEA,gBAAgB;YACd,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,eAAe,CAAC;YACd,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE;gBACrB,aAAa,CAAC,MAAM,EAAE,QAAQ;gBAC9B,UAAU;YACZ;QACF;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,wQAAA,CAAA,QAAK,CAAC,OAAO;AACf;AAGO,MAAM,kBAAkB,CAC7B,MACA,OACA,aACA,WAAmB,IAAI;IAEvB,MAAM,gBAAgB,wQAAA,CAAA,QAAK,CAAC,KAAK;IACjC,cAAc,OAAO;QACnB;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/validation/validation-utils.ts"], "sourcesContent": ["// Validation utilities for real-time form validation and error handling\n// Provides consistent validation feedback across all billing forms\n\nimport { z } from 'zod';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Validation result interface\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: ValidationError[];\n  warnings: ValidationWarning[];\n}\n\nexport interface ValidationError {\n  field: string;\n  message: string;\n  code: string;\n  severity: 'error' | 'warning';\n}\n\nexport interface ValidationWarning {\n  field: string;\n  message: string;\n  suggestion?: string;\n}\n\n// Real-time validation debounce utility\nexport class ValidationDebouncer {\n  private timeouts: Map<string, NodeJS.Timeout> = new Map();\n  private readonly delay: number;\n\n  constructor(delay: number = 500) {\n    this.delay = delay;\n  }\n\n  debounce<T extends any[]>(\n    key: string,\n    callback: (...args: T) => void,\n    ...args: T\n  ): void {\n    // Clear existing timeout for this key\n    const existingTimeout = this.timeouts.get(key);\n    if (existingTimeout) {\n      clearTimeout(existingTimeout);\n    }\n\n    // Set new timeout\n    const timeout = setTimeout(() => {\n      callback(...args);\n      this.timeouts.delete(key);\n    }, this.delay);\n\n    this.timeouts.set(key, timeout);\n  }\n\n  clear(key?: string): void {\n    if (key) {\n      const timeout = this.timeouts.get(key);\n      if (timeout) {\n        clearTimeout(timeout);\n        this.timeouts.delete(key);\n      }\n    } else {\n      // Clear all timeouts\n      this.timeouts.forEach(timeout => clearTimeout(timeout));\n      this.timeouts.clear();\n    }\n  }\n}\n\n// Field-level validation utility\nexport class FieldValidator {\n  private schema: z.ZodSchema;\n  private debouncer: ValidationDebouncer;\n\n  constructor(schema: z.ZodSchema, debounceDelay: number = 300) {\n    this.schema = schema;\n    this.debouncer = new ValidationDebouncer(debounceDelay);\n  }\n\n  validateField(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    this.debouncer.debounce(\n      fieldPath,\n      this.performFieldValidation.bind(this),\n      fieldPath,\n      value,\n      fullData,\n      onValidation\n    );\n  }\n\n  private performFieldValidation(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    try {\n      // Create a partial object with just this field\n      const fieldData = this.setNestedValue({}, fieldPath, value);\n      \n      // Merge with existing data\n      const testData = { ...fullData, ...fieldData };\n      \n      // Validate the full object but focus on this field\n      const result = this.schema.safeParse(testData);\n      \n      const fieldErrors = result.success \n        ? []\n        : result.error.errors\n            .filter(error => error.path.join('.') === fieldPath)\n            .map(error => ({\n              field: fieldPath,\n              message: error.message,\n              code: error.code,\n              severity: 'error' as const,\n            }));\n\n      const warnings = this.generateWarnings(fieldPath, value, fullData);\n\n      const validationResult: ValidationResult = {\n        isValid: fieldErrors.length === 0,\n        errors: fieldErrors,\n        warnings,\n      };\n\n      if (onValidation) {\n        onValidation(validationResult);\n      }\n    } catch (error) {\n      console.error('Field validation error:', error);\n      if (onValidation) {\n        onValidation({\n          isValid: false,\n          errors: [{\n            field: fieldPath,\n            message: '验证过程中发生错误',\n            code: 'VALIDATION_ERROR',\n            severity: 'error',\n          }],\n          warnings: [],\n        });\n      }\n    }\n  }\n\n  private setNestedValue(obj: any, path: string, value: any): any {\n    const keys = path.split('.');\n    let current = obj;\n    \n    for (let i = 0; i < keys.length - 1; i++) {\n      const key = keys[i];\n      if (!(key in current)) {\n        current[key] = {};\n      }\n      current = current[key];\n    }\n    \n    current[keys[keys.length - 1]] = value;\n    return obj;\n  }\n\n  private generateWarnings(fieldPath: string, value: any, fullData: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate context-specific warnings\n    switch (fieldPath) {\n      case 'amount':\n        if (typeof value === 'number' && value > 10000) {\n          warnings.push({\n            field: fieldPath,\n            message: '金额较大，请确认是否正确',\n            suggestion: '检查金额是否输入正确',\n          });\n        }\n        break;\n\n      case 'dueDate':\n        if (value) {\n          const dueDate = new Date(value);\n          const today = new Date();\n          const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n          \n          if (daysDiff > 365) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期距离现在超过一年',\n              suggestion: '考虑设置更近的到期日期',\n            });\n          } else if (daysDiff < 7) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期较近',\n              suggestion: '确保有足够时间处理账单',\n            });\n          }\n        }\n        break;\n\n      case 'discountAmount':\n        if (typeof value === 'number' && value > 0 && fullData.items) {\n          const subtotal = fullData.items.reduce((sum: number, item: any) => {\n            return sum + (item.quantity || 0) * (item.unitPrice || 0);\n          }, 0);\n          \n          if (value > subtotal * 0.5) {\n            warnings.push({\n              field: fieldPath,\n              message: '折扣金额超过小计的50%',\n              suggestion: '确认折扣金额是否正确',\n            });\n          }\n        }\n        break;\n\n      case 'unitPrice':\n        if (typeof value === 'number' && value === 0) {\n          warnings.push({\n            field: fieldPath,\n            message: '单价为0，确认是否为免费项目',\n            suggestion: '如果不是免费项目，请输入正确单价',\n          });\n        }\n        break;\n    }\n\n    return warnings;\n  }\n\n  cleanup(): void {\n    this.debouncer.clear();\n  }\n}\n\n// Form-level validation utility\nexport class FormValidator {\n  private schema: z.ZodSchema;\n  private fieldValidators: Map<string, FieldValidator> = new Map();\n\n  constructor(schema: z.ZodSchema) {\n    this.schema = schema;\n  }\n\n  validateForm(data: any): ValidationResult {\n    try {\n      const result = this.schema.safeParse(data);\n      \n      if (result.success) {\n        return {\n          isValid: true,\n          errors: [],\n          warnings: this.generateFormWarnings(data),\n        };\n      }\n\n      const errors = result.error.errors.map(error => ({\n        field: error.path.join('.'),\n        message: error.message,\n        code: error.code,\n        severity: 'error' as const,\n      }));\n\n      return {\n        isValid: false,\n        errors,\n        warnings: this.generateFormWarnings(data),\n      };\n    } catch (error) {\n      console.error('Form validation error:', error);\n      return {\n        isValid: false,\n        errors: [{\n          field: 'form',\n          message: '表单验证过程中发生错误',\n          code: 'FORM_VALIDATION_ERROR',\n          severity: 'error',\n        }],\n        warnings: [],\n      };\n    }\n  }\n\n  private generateFormWarnings(data: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate form-level warnings\n    if (data.items && Array.isArray(data.items)) {\n      const totalItems = data.items.length;\n      if (totalItems > 20) {\n        warnings.push({\n          field: 'items',\n          message: `账单包含${totalItems}个项目，较多`,\n          suggestion: '考虑合并相似项目或分拆为多个账单',\n        });\n      }\n\n      const totalAmount = data.items.reduce((sum: number, item: any) => {\n        return sum + (item.quantity || 0) * (item.unitPrice || 0);\n      }, 0);\n\n      if (totalAmount > 50000) {\n        warnings.push({\n          field: 'form',\n          message: '账单总金额较大',\n          suggestion: '确认金额计算是否正确',\n        });\n      }\n    }\n\n    return warnings;\n  }\n\n  getFieldValidator(fieldPath: string): FieldValidator {\n    if (!this.fieldValidators.has(fieldPath)) {\n      this.fieldValidators.set(fieldPath, new FieldValidator(this.schema));\n    }\n    return this.fieldValidators.get(fieldPath)!;\n  }\n\n  cleanup(): void {\n    this.fieldValidators.forEach(validator => validator.cleanup());\n    this.fieldValidators.clear();\n  }\n}\n\n// Validation error display utility\nexport const displayValidationErrors = (errors: ValidationError[]): void => {\n  errors.forEach(error => {\n    if (error.severity === 'error') {\n      billingNotifications.validation.requiredField(error.field);\n    }\n  });\n};\n\n// Business logic validation\nexport const validateBusinessRules = {\n  // Check if bill can be marked as paid\n  canMarkAsPaid: (bill: any): { valid: boolean; reason?: string } => {\n    if ((bill.remainingAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '账单还有未支付金额，无法标记为已支付',\n      };\n    }\n    return { valid: true };\n  },\n\n  // Check if payment amount is valid for bill\n  isValidPaymentAmount: (amount: number, bill: any): { valid: boolean; reason?: string } => {\n    const remainingAmount = bill.remainingAmount || 0;\n    \n    if (amount > remainingAmount) {\n      return {\n        valid: false,\n        reason: `支付金额不能超过待付金额 $${remainingAmount.toFixed(2)}`,\n      };\n    }\n    \n    if (amount <= 0) {\n      return {\n        valid: false,\n        reason: '支付金额必须大于0',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill can be deleted\n  canDeleteBill: (bill: any): { valid: boolean; reason?: string } => {\n    if (bill.status === 'paid') {\n      return {\n        valid: false,\n        reason: '已支付的账单不能删除',\n      };\n    }\n    \n    if ((bill.paidAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '已有支付记录的账单不能删除',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill status transition is valid\n  isValidStatusTransition: (fromStatus: string, toStatus: string): { valid: boolean; reason?: string } => {\n    const validTransitions: Record<string, string[]> = {\n      draft: ['sent', 'cancelled'],\n      sent: ['confirmed', 'cancelled'],\n      confirmed: ['paid', 'cancelled'],\n      paid: [], // No transitions from paid\n      cancelled: [], // No transitions from cancelled\n    };\n\n    const allowedTransitions = validTransitions[fromStatus] || [];\n    \n    if (!allowedTransitions.includes(toStatus)) {\n      return {\n        valid: false,\n        reason: `不能从\"${fromStatus}\"状态转换到\"${toStatus}\"状态`,\n      };\n    }\n    \n    return { valid: true };\n  },\n};\n\n// Export validation constants\nexport const VALIDATION_CONSTANTS = {\n  MAX_BILL_AMOUNT: 999999.99,\n  MAX_ITEMS_PER_BILL: 50,\n  MAX_DESCRIPTION_LENGTH: 200,\n  MAX_NOTES_LENGTH: 1000,\n  MIN_PAYMENT_AMOUNT: 0.01,\n  MAX_DISCOUNT_RATE: 100,\n  DEBOUNCE_DELAY: 300,\n} as const;\n"], "names": [], "mappings": "AAAA,wEAAwE;AACxE,mEAAmE;;;;;;;;;AAGnE;;AAuBO,MAAM;IACH,WAAwC,IAAI,MAAM;IACzC,MAAc;IAE/B,YAAY,QAAgB,GAAG,CAAE;QAC/B,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,SACE,GAAW,EACX,QAA8B,EAC9B,GAAG,IAAO,EACJ;QACN,sCAAsC;QACtC,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAC1C,IAAI,iBAAiB;YACnB,aAAa;QACf;QAEA,kBAAkB;QAClB,MAAM,UAAU,WAAW;YACzB,YAAY;YACZ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACvB,GAAG,IAAI,CAAC,KAAK;QAEb,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK;IACzB;IAEA,MAAM,GAAY,EAAQ;QACxB,IAAI,KAAK;YACP,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;YAClC,IAAI,SAAS;gBACX,aAAa;gBACb,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACvB;QACF,OAAO;YACL,qBAAqB;YACrB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA,UAAW,aAAa;YAC9C,IAAI,CAAC,QAAQ,CAAC,KAAK;QACrB;IACF;AACF;AAGO,MAAM;IACH,OAAoB;IACpB,UAA+B;IAEvC,YAAY,MAAmB,EAAE,gBAAwB,GAAG,CAAE;QAC5D,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB;IAC3C;IAEA,cACE,SAAiB,EACjB,KAAU,EACV,QAAa,EACb,YAAiD,EAC3C;QACN,IAAI,CAAC,SAAS,CAAC,QAAQ,CACrB,WACA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,GACrC,WACA,OACA,UACA;IAEJ;IAEQ,uBACN,SAAiB,EACjB,KAAU,EACV,QAAa,EACb,YAAiD,EAC3C;QACN,IAAI;YACF,+CAA+C;YAC/C,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,WAAW;YAErD,2BAA2B;YAC3B,MAAM,WAAW;gBAAE,GAAG,QAAQ;gBAAE,GAAG,SAAS;YAAC;YAE7C,mDAAmD;YACnD,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAErC,MAAM,cAAc,OAAO,OAAO,GAC9B,EAAE,GACF,OAAO,KAAK,CAAC,MAAM,CAChB,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,WACzC,GAAG,CAAC,CAAA,QAAS,CAAC;oBACb,OAAO;oBACP,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,UAAU;gBACZ,CAAC;YAEP,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,WAAW,OAAO;YAEzD,MAAM,mBAAqC;gBACzC,SAAS,YAAY,MAAM,KAAK;gBAChC,QAAQ;gBACR;YACF;YAEA,IAAI,cAAc;gBAChB,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,cAAc;gBAChB,aAAa;oBACX,SAAS;oBACT,QAAQ;wBAAC;4BACP,OAAO;4BACP,SAAS;4BACT,MAAM;4BACN,UAAU;wBACZ;qBAAE;oBACF,UAAU,EAAE;gBACd;YACF;QACF;IACF;IAEQ,eAAe,GAAQ,EAAE,IAAY,EAAE,KAAU,EAAO;QAC9D,MAAM,OAAO,KAAK,KAAK,CAAC;QACxB,IAAI,UAAU;QAEd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;YACxC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,CAAC,OAAO,OAAO,GAAG;gBACrB,OAAO,CAAC,IAAI,GAAG,CAAC;YAClB;YACA,UAAU,OAAO,CAAC,IAAI;QACxB;QAEA,OAAO,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG;QACjC,OAAO;IACT;IAEQ,iBAAiB,SAAiB,EAAE,KAAU,EAAE,QAAa,EAAuB;QAC1F,MAAM,WAAgC,EAAE;QAExC,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,IAAI,OAAO,UAAU,YAAY,QAAQ,OAAO;oBAC9C,SAAS,IAAI,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,YAAY;oBACd;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO;oBACT,MAAM,UAAU,IAAI,KAAK;oBACzB,MAAM,QAAQ,IAAI;oBAClB,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;oBAEvF,IAAI,WAAW,KAAK;wBAClB,SAAS,IAAI,CAAC;4BACZ,OAAO;4BACP,SAAS;4BACT,YAAY;wBACd;oBACF,OAAO,IAAI,WAAW,GAAG;wBACvB,SAAS,IAAI,CAAC;4BACZ,OAAO;4BACP,SAAS;4BACT,YAAY;wBACd;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO,UAAU,YAAY,QAAQ,KAAK,SAAS,KAAK,EAAE;oBAC5D,MAAM,WAAW,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,KAAa;wBACnD,OAAO,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC;oBAC1D,GAAG;oBAEH,IAAI,QAAQ,WAAW,KAAK;wBAC1B,SAAS,IAAI,CAAC;4BACZ,OAAO;4BACP,SAAS;4BACT,YAAY;wBACd;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO,UAAU,YAAY,UAAU,GAAG;oBAC5C,SAAS,IAAI,CAAC;wBACZ,OAAO;wBACP,SAAS;wBACT,YAAY;oBACd;gBACF;gBACA;QACJ;QAEA,OAAO;IACT;IAEA,UAAgB;QACd,IAAI,CAAC,SAAS,CAAC,KAAK;IACtB;AACF;AAGO,MAAM;IACH,OAAoB;IACpB,kBAA+C,IAAI,MAAM;IAEjE,YAAY,MAAmB,CAAE;QAC/B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,aAAa,IAAS,EAAoB;QACxC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAErC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO;oBACL,SAAS;oBACT,QAAQ,EAAE;oBACV,UAAU,IAAI,CAAC,oBAAoB,CAAC;gBACtC;YACF;YAEA,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC/C,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC;oBACvB,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,UAAU;gBACZ,CAAC;YAED,OAAO;gBACL,SAAS;gBACT;gBACA,UAAU,IAAI,CAAC,oBAAoB,CAAC;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAC;wBACP,OAAO;wBACP,SAAS;wBACT,MAAM;wBACN,UAAU;oBACZ;iBAAE;gBACF,UAAU,EAAE;YACd;QACF;IACF;IAEQ,qBAAqB,IAAS,EAAuB;QAC3D,MAAM,WAAgC,EAAE;QAExC,+BAA+B;QAC/B,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,GAAG;YAC3C,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM;YACpC,IAAI,aAAa,IAAI;gBACnB,SAAS,IAAI,CAAC;oBACZ,OAAO;oBACP,SAAS,CAAC,IAAI,EAAE,WAAW,MAAM,CAAC;oBAClC,YAAY;gBACd;YACF;YAEA,MAAM,cAAc,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAa;gBAClD,OAAO,MAAM,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC;YAC1D,GAAG;YAEH,IAAI,cAAc,OAAO;gBACvB,SAAS,IAAI,CAAC;oBACZ,OAAO;oBACP,SAAS;oBACT,YAAY;gBACd;YACF;QACF;QAEA,OAAO;IACT;IAEA,kBAAkB,SAAiB,EAAkB;QACnD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY;YACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,IAAI,eAAe,IAAI,CAAC,MAAM;QACpE;QACA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;IAClC;IAEA,UAAgB;QACd,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA,YAAa,UAAU,OAAO;QAC3D,IAAI,CAAC,eAAe,CAAC,KAAK;IAC5B;AACF;AAGO,MAAM,0BAA0B,CAAC;IACtC,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,MAAM,QAAQ,KAAK,SAAS;YAC9B,sIAAA,CAAA,uBAAoB,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,KAAK;QAC3D;IACF;AACF;AAGO,MAAM,wBAAwB;IACnC,sCAAsC;IACtC,eAAe,CAAC;QACd,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,GAAG;YACnC,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QACA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,4CAA4C;IAC5C,sBAAsB,CAAC,QAAgB;QACrC,MAAM,kBAAkB,KAAK,eAAe,IAAI;QAEhD,IAAI,SAAS,iBAAiB;YAC5B,OAAO;gBACL,OAAO;gBACP,QAAQ,CAAC,cAAc,EAAE,gBAAgB,OAAO,CAAC,IAAI;YACvD;QACF;QAEA,IAAI,UAAU,GAAG;YACf,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,+BAA+B;IAC/B,eAAe,CAAC;QACd,IAAI,KAAK,MAAM,KAAK,QAAQ;YAC1B,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,IAAI,GAAG;YAC9B,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;IAEA,2CAA2C;IAC3C,yBAAyB,CAAC,YAAoB;QAC5C,MAAM,mBAA6C;YACjD,OAAO;gBAAC;gBAAQ;aAAY;YAC5B,MAAM;gBAAC;gBAAa;aAAY;YAChC,WAAW;gBAAC;gBAAQ;aAAY;YAChC,MAAM,EAAE;YACR,WAAW,EAAE;QACf;QAEA,MAAM,qBAAqB,gBAAgB,CAAC,WAAW,IAAI,EAAE;QAE7D,IAAI,CAAC,mBAAmB,QAAQ,CAAC,WAAW;YAC1C,OAAO;gBACL,OAAO;gBACP,QAAQ,CAAC,IAAI,EAAE,WAAW,OAAO,EAAE,SAAS,GAAG,CAAC;YAClD;QACF;QAEA,OAAO;YAAE,OAAO;QAAK;IACvB;AACF;AAGO,MAAM,uBAAuB;IAClC,iBAAiB;IACjB,oBAAoB;IACpB,wBAAwB;IACxB,kBAAkB;IAClB,oBAAoB;IACpB,mBAAmB;IACnB,gBAAgB;AAClB", "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\r\n  return (\r\n    <textarea\r\n      data-slot='textarea'\r\n      className={cn(\r\n        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1992, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card'\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-header'\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-title'\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-action'\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-content'\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-footer'\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot='form-item'\r\n        className={cn('grid gap-2', className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot='form-label'\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } =\r\n    useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot='form-control'\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot='form-description'\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot='form-message'\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,uPAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6VAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6VAAC,uPAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,oTAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uPAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,oTAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6VAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6VAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6VAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,6VAAC,oSAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6VAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/receipt.tsx"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { Separator } from '@/components/ui/separator';\nimport { Badge } from '@/components/ui/badge';\nimport { Payment, Bill } from '@/types/clinic';\nimport { billingUtils } from '@/lib/api/billing';\n\ninterface ReceiptProps {\n  payment: Payment;\n  bill?: Bill;\n  clinicInfo?: {\n    name: string;\n    address: string;\n    phone: string;\n    email?: string;\n    taxId?: string;\n  };\n}\n\n// Default clinic information - this would typically come from settings\nconst defaultClinicInfo = {\n  name: '美丽诊所',\n  address: '北京市朝阳区美丽街123号',\n  phone: '010-12345678',\n  email: '<EMAIL>',\n  taxId: '91110000000000000X',\n};\n\nexport const Receipt = forwardRef<HTMLDivElement, ReceiptProps>(\n  ({ payment, bill, clinicInfo = defaultClinicInfo }, ref) => {\n    const patient = typeof payment.patient === 'object' ? payment.patient : null;\n    const paymentBill = bill || (typeof payment.bill === 'object' ? payment.bill : null);\n\n    return (\n      <div ref={ref} className=\"max-w-md mx-auto bg-white\">\n        <Card className=\"shadow-none border-none\">\n          <CardHeader className=\"text-center pb-4\">\n            {/* Clinic Header */}\n            <div className=\"space-y-1\">\n              <h1 className=\"text-xl font-bold\">{clinicInfo.name}</h1>\n              <p className=\"text-sm text-muted-foreground\">{clinicInfo.address}</p>\n              <p className=\"text-sm text-muted-foreground\">\n                电话: {clinicInfo.phone}\n                {clinicInfo.email && ` | 邮箱: ${clinicInfo.email}`}\n              </p>\n              {clinicInfo.taxId && (\n                <p className=\"text-xs text-muted-foreground\">\n                  税号: {clinicInfo.taxId}\n                </p>\n              )}\n            </div>\n            \n            <Separator className=\"my-4\" />\n            \n            {/* Receipt Title */}\n            <div className=\"space-y-2\">\n              <h2 className=\"text-lg font-semibold\">收款收据</h2>\n              <div className=\"flex justify-between text-sm\">\n                <span>收据编号:</span>\n                <span className=\"font-mono\">{payment.receiptNumber || '待生成'}</span>\n              </div>\n              <div className=\"flex justify-between text-sm\">\n                <span>支付编号:</span>\n                <span className=\"font-mono\">{payment.paymentNumber}</span>\n              </div>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"space-y-4\">\n            {/* Payment Information */}\n            <div className=\"space-y-2\">\n              <h3 className=\"font-medium text-sm border-b pb-1\">支付信息</h3>\n              \n              <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">患者姓名:</span>\n                  <span>{patient?.fullName || '未知患者'}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">支付日期:</span>\n                  <span>{new Date(payment.paymentDate).toLocaleDateString('zh-CN')}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">支付方式:</span>\n                  <span>{billingUtils.getPaymentMethodName(payment.paymentMethod)}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">支付状态:</span>\n                  <Badge variant={payment.paymentStatus === 'completed' ? 'default' : 'secondary'}>\n                    {billingUtils.getPaymentStatusName(payment.paymentStatus)}\n                  </Badge>\n                </div>\n              </div>\n\n              {payment.transactionId && (\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">交易ID:</span>\n                  <span className=\"font-mono text-xs\">{payment.transactionId}</span>\n                </div>\n              )}\n            </div>\n\n            <Separator />\n\n            {/* Bill Information */}\n            {paymentBill && (\n              <div className=\"space-y-2\">\n                <h3 className=\"font-medium text-sm border-b pb-1\">账单信息</h3>\n                \n                <div className=\"space-y-1 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">账单编号:</span>\n                    <span className=\"font-mono\">{paymentBill.billNumber}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">账单类型:</span>\n                    <span>{billingUtils.getBillStatusName(paymentBill.billType)}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">服务描述:</span>\n                    <span className=\"text-right max-w-32 truncate\">\n                      {paymentBill.description}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">账单总额:</span>\n                    <span>{billingUtils.formatCurrency(paymentBill.totalAmount)}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">已支付:</span>\n                    <span className=\"text-green-600\">\n                      {billingUtils.formatCurrency(paymentBill.paidAmount || 0)}\n                    </span>\n                  </div>\n                  \n                  {(paymentBill.remainingAmount || 0) > 0 && (\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">待支付:</span>\n                      <span className=\"text-red-600\">\n                        {billingUtils.formatCurrency(paymentBill.remainingAmount || 0)}\n                      </span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n\n            <Separator />\n\n            {/* Payment Amount */}\n            <div className=\"space-y-2\">\n              <h3 className=\"font-medium text-sm border-b pb-1\">本次支付</h3>\n              \n              <div className=\"bg-muted/30 rounded-lg p-3\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-lg font-medium\">支付金额:</span>\n                  <span className=\"text-xl font-bold text-green-600\">\n                    {billingUtils.formatCurrency(payment.amount)}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Notes */}\n            {payment.notes && (\n              <>\n                <Separator />\n                <div className=\"space-y-2\">\n                  <h3 className=\"font-medium text-sm border-b pb-1\">备注</h3>\n                  <p className=\"text-sm text-muted-foreground\">{payment.notes}</p>\n                </div>\n              </>\n            )}\n\n            <Separator />\n\n            {/* Footer */}\n            <div className=\"text-center space-y-2\">\n              <p className=\"text-xs text-muted-foreground\">\n                感谢您选择{clinicInfo.name}\n              </p>\n              <p className=\"text-xs text-muted-foreground\">\n                如有疑问，请联系我们: {clinicInfo.phone}\n              </p>\n              <p className=\"text-xs text-muted-foreground\">\n                打印时间: {new Date().toLocaleString('zh-CN')}\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n);\n\nReceipt.displayName = 'Receipt';\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAqBA,uEAAuE;AACvE,MAAM,oBAAoB;IACxB,MAAM;IACN,SAAS;IACT,OAAO;IACP,OAAO;IACP,OAAO;AACT;AAEO,MAAM,wBAAU,CAAA,GAAA,oTAAA,CAAA,aAAU,AAAD,EAC9B,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,iBAAiB,EAAE,EAAE;IAClD,MAAM,UAAU,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GAAG;IACxE,MAAM,cAAc,QAAQ,CAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,GAAG,IAAI;IAEnF,qBACE,6VAAC;QAAI,KAAK;QAAK,WAAU;kBACvB,cAAA,6VAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6VAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCAEpB,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAAqB,WAAW,IAAI;;;;;;8CAClD,6VAAC;oCAAE,WAAU;8CAAiC,WAAW,OAAO;;;;;;8CAChE,6VAAC;oCAAE,WAAU;;wCAAgC;wCACtC,WAAW,KAAK;wCACpB,WAAW,KAAK,IAAI,CAAC,OAAO,EAAE,WAAW,KAAK,EAAE;;;;;;;gCAElD,WAAW,KAAK,kBACf,6VAAC;oCAAE,WAAU;;wCAAgC;wCACtC,WAAW,KAAK;;;;;;;;;;;;;sCAK3B,6VAAC,qIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAGrB,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;sDAAK;;;;;;sDACN,6VAAC;4CAAK,WAAU;sDAAa,QAAQ,aAAa,IAAI;;;;;;;;;;;;8CAExD,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;sDAAK;;;;;;sDACN,6VAAC;4CAAK,WAAU;sDAAa,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;8BAKxD,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;8DAAM,SAAS,YAAY;;;;;;;;;;;;sDAG9B,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;8DAAM,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;sDAG1D,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;8DAAM,4HAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,QAAQ,aAAa;;;;;;;;;;;;sDAGhE,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC,iIAAA,CAAA,QAAK;oDAAC,SAAS,QAAQ,aAAa,KAAK,cAAc,YAAY;8DACjE,4HAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,QAAQ,aAAa;;;;;;;;;;;;;;;;;;gCAK7D,QAAQ,aAAa,kBACpB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,6VAAC;4CAAK,WAAU;sDAAqB,QAAQ,aAAa;;;;;;;;;;;;;;;;;;sCAKhE,6VAAC,qIAAA,CAAA,YAAS;;;;;wBAGT,6BACC,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;oDAAK,WAAU;8DAAa,YAAY,UAAU;;;;;;;;;;;;sDAGrD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;8DAAM,4HAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,YAAY,QAAQ;;;;;;;;;;;;sDAG5D,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;oDAAK,WAAU;8DACb,YAAY,WAAW;;;;;;;;;;;;sDAI5B,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;8DAAM,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,YAAY,WAAW;;;;;;;;;;;;sDAG5D,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;oDAAK,WAAU;8DACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,YAAY,UAAU,IAAI;;;;;;;;;;;;wCAI1D,CAAC,YAAY,eAAe,IAAI,CAAC,IAAI,mBACpC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6VAAC;oDAAK,WAAU;8DACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,YAAY,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAQxE,6VAAC,qIAAA,CAAA,YAAS;;;;;sCAGV,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,6VAAC;gDAAK,WAAU;0DACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;wBAOlD,QAAQ,KAAK,kBACZ;;8CACE,6VAAC,qIAAA,CAAA,YAAS;;;;;8CACV,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6VAAC;4CAAE,WAAU;sDAAiC,QAAQ,KAAK;;;;;;;;;;;;;;sCAKjE,6VAAC,qIAAA,CAAA,YAAS;;;;;sCAGV,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAE,WAAU;;wCAAgC;wCACrC,WAAW,IAAI;;;;;;;8CAEvB,6VAAC;oCAAE,WAAU;;wCAAgC;wCAC9B,WAAW,KAAK;;;;;;;8CAE/B,6VAAC;oCAAE,WAAU;;wCAAgC;wCACpC,IAAI,OAAO,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;AAGF,QAAQ,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/receipt-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Receipt } from './receipt';\nimport { Payment, Bill } from '@/types/clinic';\nimport { IconPrinter, IconDownload, IconX } from '@tabler/icons-react';\nimport { toast } from 'sonner';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\ninterface ReceiptDialogProps {\n  payment: Payment | null;\n  bill?: Bill;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function ReceiptDialog({ payment, bill, isOpen, onClose }: ReceiptDialogProps) {\n  const receiptRef = useRef<HTMLDivElement>(null);\n\n  const handlePrint = () => {\n    if (!receiptRef.current) return;\n\n    const printWindow = window.open('', '_blank');\n    if (!printWindow) {\n      billingNotifications.receipt.printError();\n      return;\n    }\n\n    const receiptContent = receiptRef.current.innerHTML;\n    \n    printWindow.document.write(`\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>收据 - ${payment?.receiptNumber || payment?.paymentNumber}</title>\n          <meta charset=\"utf-8\">\n          <style>\n            body {\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n              margin: 0;\n              padding: 20px;\n              background: white;\n            }\n            .max-w-md {\n              max-width: 28rem;\n            }\n            .mx-auto {\n              margin-left: auto;\n              margin-right: auto;\n            }\n            .bg-white {\n              background-color: white;\n            }\n            .shadow-none {\n              box-shadow: none;\n            }\n            .border-none {\n              border: none;\n            }\n            .text-center {\n              text-align: center;\n            }\n            .text-xl {\n              font-size: 1.25rem;\n            }\n            .text-lg {\n              font-size: 1.125rem;\n            }\n            .text-sm {\n              font-size: 0.875rem;\n            }\n            .text-xs {\n              font-size: 0.75rem;\n            }\n            .font-bold {\n              font-weight: 700;\n            }\n            .font-semibold {\n              font-weight: 600;\n            }\n            .font-medium {\n              font-weight: 500;\n            }\n            .font-mono {\n              font-family: ui-monospace, SFMono-Regular, monospace;\n            }\n            .space-y-1 > * + * {\n              margin-top: 0.25rem;\n            }\n            .space-y-2 > * + * {\n              margin-top: 0.5rem;\n            }\n            .space-y-4 > * + * {\n              margin-top: 1rem;\n            }\n            .pb-4 {\n              padding-bottom: 1rem;\n            }\n            .pb-1 {\n              padding-bottom: 0.25rem;\n            }\n            .p-3 {\n              padding: 0.75rem;\n            }\n            .my-4 {\n              margin-top: 1rem;\n              margin-bottom: 1rem;\n            }\n            .border-b {\n              border-bottom: 1px solid #e5e7eb;\n            }\n            .grid {\n              display: grid;\n            }\n            .grid-cols-2 {\n              grid-template-columns: repeat(2, minmax(0, 1fr));\n            }\n            .gap-2 {\n              gap: 0.5rem;\n            }\n            .flex {\n              display: flex;\n            }\n            .justify-between {\n              justify-content: space-between;\n            }\n            .items-center {\n              align-items: center;\n            }\n            .text-right {\n              text-align: right;\n            }\n            .max-w-32 {\n              max-width: 8rem;\n            }\n            .truncate {\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n            .text-muted-foreground {\n              color: #6b7280;\n            }\n            .text-green-600 {\n              color: #059669;\n            }\n            .text-red-600 {\n              color: #dc2626;\n            }\n            .bg-muted\\\\/30 {\n              background-color: rgba(243, 244, 246, 0.3);\n            }\n            .rounded-lg {\n              border-radius: 0.5rem;\n            }\n            hr {\n              border: none;\n              border-top: 1px solid #e5e7eb;\n              margin: 1rem 0;\n            }\n            @media print {\n              body {\n                padding: 0;\n              }\n              .no-print {\n                display: none;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          ${receiptContent}\n        </body>\n      </html>\n    `);\n\n    printWindow.document.close();\n    printWindow.focus();\n    \n    // Wait for content to load then print\n    setTimeout(() => {\n      printWindow.print();\n      printWindow.close();\n    }, 250);\n\n    billingNotifications.receipt.printed(payment?.receiptNumber || payment?.paymentNumber || '');\n  };\n\n  const handleDownloadPDF = async () => {\n    try {\n      // This would typically use a library like jsPDF or html2pdf\n      // For now, we'll show a placeholder message\n      billingNotifications.system.featureNotImplemented('PDF下载');\n      \n      // Example implementation with html2pdf (would need to install the library):\n      /*\n      const html2pdf = (await import('html2pdf.js')).default;\n      const element = receiptRef.current;\n      \n      const opt = {\n        margin: 0.5,\n        filename: `receipt-${payment?.receiptNumber || payment?.paymentNumber}.pdf`,\n        image: { type: 'jpeg', quality: 0.98 },\n        html2canvas: { scale: 2 },\n        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }\n      };\n      \n      html2pdf().set(opt).from(element).save();\n      */\n    } catch (error) {\n      console.error('PDF generation failed:', error);\n      billingNotifications.receipt.downloadError();\n    }\n  };\n\n  if (!payment) {\n    return null;\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-lg max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <div className=\"flex items-center justify-between\">\n            <DialogTitle>\n              收据 - {payment.receiptNumber || payment.paymentNumber}\n            </DialogTitle>\n            <div className=\"flex items-center gap-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={handlePrint}>\n                <IconPrinter className=\"h-4 w-4 mr-2\" />\n                打印\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" onClick={handleDownloadPDF}>\n                <IconDownload className=\"h-4 w-4 mr-2\" />\n                下载PDF\n              </Button>\n              <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n                <IconX className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </DialogHeader>\n        \n        <div className=\"mt-4\">\n          <Receipt ref={receiptRef} payment={payment} bill={bill} />\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AATA;;;;;;;;AAkBO,SAAS,cAAc,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAsB;IAClF,MAAM,aAAa,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,OAAO,EAAE;QAEzB,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;YAChB,sIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,UAAU;YACvC;QACF;QAEA,MAAM,iBAAiB,WAAW,OAAO,CAAC,SAAS;QAEnD,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAC;;;;sBAIV,EAAE,SAAS,iBAAiB,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAyI/D,EAAE,eAAe;;;IAGvB,CAAC;QAED,YAAY,QAAQ,CAAC,KAAK;QAC1B,YAAY,KAAK;QAEjB,sCAAsC;QACtC,WAAW;YACT,YAAY,KAAK;YACjB,YAAY,KAAK;QACnB,GAAG;QAEH,sIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,iBAAiB,SAAS,iBAAiB;IAC3F;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,4DAA4D;YAC5D,4CAA4C;YAC5C,sIAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,qBAAqB,CAAC;QAElD,4EAA4E;QAC5E;;;;;;;;;;;;;MAaA,GACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,sIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,aAAa;QAC5C;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6VAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6VAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6VAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,cAAW;;oCAAC;oCACL,QAAQ,aAAa,IAAI,QAAQ,aAAa;;;;;;;0CAEtD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,6VAAC,0TAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG1C,6VAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,6VAAC,4TAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG3C,6VAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,SAAS;kDACzC,cAAA,6VAAC,8SAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMzB,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC,wIAAA,CAAA,UAAO;wBAAC,KAAK;wBAAY,SAAS;wBAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;AAK5D", "debugId": null}}, {"offset": {"line": 3232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/payment-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { paymentFormSchema, PaymentFormData } from '@/lib/validation/billing-schemas';\nimport { FormValidator, validateBusinessRules } from '@/lib/validation/validation-utils';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { \n  IconCreditCard, \n  IconCash, \n  IconDeviceMobile, \n  IconBrandAlipay,\n  IconBuildingBank,\n  IconCalendar,\n  IconReceipt,\n  IconX\n} from '@tabler/icons-react';\nimport { Bill, Payment } from '@/types/clinic';\nimport { paymentsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { toast } from 'sonner';\nimport { ReceiptDialog } from './receipt-dialog';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Remove the local schema since we're now using the centralized one\n\ninterface PaymentFormProps {\n  bill: Bill;\n  onSuccess?: (payment: Payment) => void;\n  onCancel?: () => void;\n  isOpen?: boolean;\n}\n\n// Payment method options with icons and labels\nconst paymentMethods = [\n  {\n    value: 'cash',\n    label: '现金',\n    icon: IconCash,\n    description: '现金支付',\n    requiresTransactionId: false,\n  },\n  {\n    value: 'card',\n    label: '银行卡',\n    icon: IconCreditCard,\n    description: '银行卡刷卡支付',\n    requiresTransactionId: true,\n  },\n  {\n    value: 'wechat',\n    label: '微信支付',\n    icon: IconDeviceMobile,\n    description: '微信扫码支付',\n    requiresTransactionId: true,\n  },\n  {\n    value: 'alipay',\n    label: '支付宝',\n    icon: IconBrandAlipay,\n    description: '支付宝扫码支付',\n    requiresTransactionId: true,\n  },\n  {\n    value: 'transfer',\n    label: '银行转账',\n    icon: IconBuildingBank,\n    description: '银行转账支付',\n    requiresTransactionId: true,\n  },\n  {\n    value: 'installment',\n    label: '分期付款',\n    icon: IconCalendar,\n    description: '分期付款',\n    requiresTransactionId: false,\n  },\n];\n\nexport function PaymentForm({ bill, onSuccess, onCancel, isOpen = true }: PaymentFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [selectedMethod, setSelectedMethod] = useState<string>('');\n  const [completedPayment, setCompletedPayment] = useState<Payment | null>(null);\n  const [showReceipt, setShowReceipt] = useState(false);\n\n  const form = useForm<PaymentFormData>({\n    resolver: zodResolver(paymentFormSchema),\n    defaultValues: {\n      amount: bill.remainingAmount || 0,\n      paymentMethod: 'cash',\n      transactionId: '',\n      notes: '',\n    },\n  });\n\n  const selectedPaymentMethod = paymentMethods.find(method => method.value === selectedMethod);\n  const remainingAmount = bill.remainingAmount || 0;\n  const maxPaymentAmount = remainingAmount;\n\n  const onSubmit = async (data: PaymentFormData) => {\n    try {\n      setIsSubmitting(true);\n\n      // Business rule validation\n      const amountValidation = validateBusinessRules.isValidPaymentAmount(data.amount, bill);\n      if (!amountValidation.valid) {\n        billingNotifications.payment.validationError(amountValidation.reason || '支付金额无效');\n        return;\n      }\n\n      // Process payment\n      const payment = await paymentsAPI.processPayment({\n        bill: bill.id,\n        patient: typeof bill.patient === 'object' ? bill.patient.id : bill.patientId,\n        amount: data.amount,\n        paymentMethod: data.paymentMethod,\n        transactionId: data.transactionId || undefined,\n        notes: data.notes || undefined,\n      });\n\n      billingNotifications.payment.processed(payment);\n\n      // Store payment for receipt display\n      setCompletedPayment(payment);\n      setShowReceipt(true);\n\n      if (onSuccess) {\n        onSuccess(payment);\n      }\n\n      // Reset form\n      form.reset();\n      \n    } catch (error) {\n      console.error('Payment processing failed:', error);\n      const errorMessage = error instanceof BillingAPIError\n        ? error.message\n        : undefined;\n      billingNotifications.payment.processError(errorMessage);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <IconReceipt className=\"h-5 w-5\" />\n              处理支付\n            </CardTitle>\n            <CardDescription>\n              为账单 {bill.billNumber} 处理支付\n            </CardDescription>\n          </div>\n          {onCancel && (\n            <Button variant=\"ghost\" size=\"sm\" onClick={onCancel}>\n              <IconX className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\">\n        {/* Bill Summary */}\n        <div className=\"bg-muted/50 rounded-lg p-4 space-y-2\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">患者:</span>\n            <span className=\"text-sm\">\n              {typeof bill.patient === 'object' ? bill.patient.fullName : '未知患者'}\n            </span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">账单总额:</span>\n            <span className=\"text-sm font-semibold\">\n              {billingUtils.formatCurrency(bill.totalAmount)}\n            </span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">已支付:</span>\n            <span className=\"text-sm text-green-600\">\n              {billingUtils.formatCurrency(bill.paidAmount || 0)}\n            </span>\n          </div>\n          <Separator />\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">待支付:</span>\n            <span className=\"text-lg font-bold text-red-600\">\n              {billingUtils.formatCurrency(remainingAmount)}\n            </span>\n          </div>\n        </div>\n\n        {/* Payment Form */}\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Payment Amount */}\n            <FormField\n              control={form.control}\n              name=\"amount\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>支付金额</FormLabel>\n                  <FormControl>\n                    <div className=\"relative\">\n                      <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n                        $\n                      </span>\n                      <Input\n                        type=\"number\"\n                        step=\"0.01\"\n                        min=\"0.01\"\n                        max={maxPaymentAmount}\n                        placeholder=\"0.00\"\n                        className=\"pl-8\"\n                        {...field}\n                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}\n                      />\n                    </div>\n                  </FormControl>\n                  <FormDescription>\n                    最大支付金额: {billingUtils.formatCurrency(maxPaymentAmount)}\n                  </FormDescription>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Payment Method */}\n            <FormField\n              control={form.control}\n              name=\"paymentMethod\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>支付方式</FormLabel>\n                  <FormControl>\n                    <Select \n                      value={field.value} \n                      onValueChange={(value) => {\n                        field.onChange(value);\n                        setSelectedMethod(value);\n                      }}\n                    >\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"选择支付方式\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {paymentMethods.map((method) => {\n                          const Icon = method.icon;\n                          return (\n                            <SelectItem key={method.value} value={method.value}>\n                              <div className=\"flex items-center gap-2\">\n                                <Icon className=\"h-4 w-4\" />\n                                <div>\n                                  <div className=\"font-medium\">{method.label}</div>\n                                  <div className=\"text-xs text-muted-foreground\">\n                                    {method.description}\n                                  </div>\n                                </div>\n                              </div>\n                            </SelectItem>\n                          );\n                        })}\n                      </SelectContent>\n                    </Select>\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Transaction ID (conditional) */}\n            {selectedPaymentMethod?.requiresTransactionId && (\n              <FormField\n                control={form.control}\n                name=\"transactionId\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>交易ID</FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"输入第三方支付平台的交易ID\"\n                        {...field}\n                      />\n                    </FormControl>\n                    <FormDescription>\n                      请输入{selectedPaymentMethod.label}的交易ID或流水号\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            )}\n\n            {/* Notes */}\n            <FormField\n              control={form.control}\n              name=\"notes\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>备注 (可选)</FormLabel>\n                  <FormControl>\n                    <Textarea\n                      placeholder=\"支付相关备注信息...\"\n                      className=\"resize-none\"\n                      rows={3}\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Action Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"flex-1\"\n              >\n                {isSubmitting ? '处理中...' : '确认支付'}\n              </Button>\n              {onCancel && (\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={onCancel}\n                  disabled={isSubmitting}\n                >\n                  取消\n                </Button>\n              )}\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n\n      {/* Receipt Dialog */}\n      <ReceiptDialog\n        payment={completedPayment}\n        bill={bill}\n        isOpen={showReceipt}\n        onClose={() => {\n          setShowReceipt(false);\n          setCompletedPayment(null);\n        }}\n      />\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAEA;AACA;AA/BA;;;;;;;;;;;;;;;;;;AA0CA,+CAA+C;AAC/C,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,OAAO;QACP,MAAM,oTAAA,CAAA,WAAQ;QACd,aAAa;QACb,uBAAuB;IACzB;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,gUAAA,CAAA,iBAAc;QACpB,aAAa;QACb,uBAAuB;IACzB;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,oUAAA,CAAA,mBAAgB;QACtB,aAAa;QACb,uBAAuB;IACzB;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,kUAAA,CAAA,kBAAe;QACrB,aAAa;QACb,uBAAuB;IACzB;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,oUAAA,CAAA,mBAAgB;QACtB,aAAa;QACb,uBAAuB;IACzB;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,4TAAA,CAAA,eAAY;QAClB,aAAa;QACb,uBAAuB;IACzB;CACD;AAEM,SAAS,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,IAAI,EAAoB;IACxF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAkB;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAmB;QACpC,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE,8IAAA,CAAA,oBAAiB;QACvC,eAAe;YACb,QAAQ,KAAK,eAAe,IAAI;YAChC,eAAe;YACf,eAAe;YACf,OAAO;QACT;IACF;IAEA,MAAM,wBAAwB,eAAe,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAC7E,MAAM,kBAAkB,KAAK,eAAe,IAAI;IAChD,MAAM,mBAAmB;IAEzB,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAEhB,2BAA2B;YAC3B,MAAM,mBAAmB,+IAAA,CAAA,wBAAqB,CAAC,oBAAoB,CAAC,KAAK,MAAM,EAAE;YACjF,IAAI,CAAC,iBAAiB,KAAK,EAAE;gBAC3B,sIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,eAAe,CAAC,iBAAiB,MAAM,IAAI;gBACxE;YACF;YAEA,kBAAkB;YAClB,MAAM,UAAU,MAAM,4HAAA,CAAA,cAAW,CAAC,cAAc,CAAC;gBAC/C,MAAM,KAAK,EAAE;gBACb,SAAS,OAAO,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,CAAC,EAAE,GAAG,KAAK,SAAS;gBAC5E,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,aAAa;gBACjC,eAAe,KAAK,aAAa,IAAI;gBACrC,OAAO,KAAK,KAAK,IAAI;YACvB;YAEA,sIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,SAAS,CAAC;YAEvC,oCAAoC;YACpC,oBAAoB;YACpB,eAAe;YAEf,IAAI,WAAW;gBACb,UAAU;YACZ;YAEA,aAAa;YACb,KAAK,KAAK;QAEZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,iBAAiB,4HAAA,CAAA,kBAAe,GACjD,MAAM,OAAO,GACb;YACJ,sIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC;QAC5C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,qBACE,6VAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6VAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;;8CACC,6VAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6VAAC,0TAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGrC,6VAAC,gIAAA,CAAA,kBAAe;;wCAAC;wCACV,KAAK,UAAU;wCAAC;;;;;;;;;;;;;wBAGxB,0BACC,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;sCACzC,cAAA,6VAAC,8SAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMzB,6VAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6VAAC;wCAAK,WAAU;kDACb,OAAO,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,CAAC,QAAQ,GAAG;;;;;;;;;;;;0CAGhE,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6VAAC;wCAAK,WAAU;kDACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,WAAW;;;;;;;;;;;;0CAGjD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6VAAC;wCAAK,WAAU;kDACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,UAAU,IAAI;;;;;;;;;;;;0CAGpD,6VAAC,qIAAA,CAAA,YAAS;;;;;0CACV,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6VAAC;wCAAK,WAAU;kDACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC;;;;;;;;;;;;;;;;;;kCAMnC,6VAAC,gIAAA,CAAA,OAAI;wBAAE,GAAG,IAAI;kCACZ,cAAA,6VAAC;4BAAK,UAAU,KAAK,YAAY,CAAC;4BAAW,WAAU;;8CAErD,6VAAC,gIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;8DACP,6VAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6VAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAK,WAAU;0EAA2E;;;;;;0EAG3F,6VAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,MAAK;gEACL,KAAI;gEACJ,KAAK;gEACL,aAAY;gEACZ,WAAU;gEACT,GAAG,KAAK;gEACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;8DAIpE,6VAAC,gIAAA,CAAA,kBAAe;;wDAAC;wDACN,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC;;;;;;;8DAEvC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAMlB,6VAAC,gIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;8DACP,6VAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6VAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,6VAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,MAAM,KAAK;wDAClB,eAAe,CAAC;4DACd,MAAM,QAAQ,CAAC;4DACf,kBAAkB;wDACpB;;0EAEA,6VAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6VAAC,kIAAA,CAAA,gBAAa;0EACX,eAAe,GAAG,CAAC,CAAC;oEACnB,MAAM,OAAO,OAAO,IAAI;oEACxB,qBACE,6VAAC,kIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAChD,cAAA,6VAAC;4EAAI,WAAU;;8FACb,6VAAC;oFAAK,WAAU;;;;;;8FAChB,6VAAC;;sGACC,6VAAC;4FAAI,WAAU;sGAAe,OAAO,KAAK;;;;;;sGAC1C,6VAAC;4FAAI,WAAU;sGACZ,OAAO,WAAW;;;;;;;;;;;;;;;;;;uEANV,OAAO,KAAK;;;;;gEAYjC;;;;;;;;;;;;;;;;;8DAIN,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;gCAMjB,uBAAuB,uCACtB,6VAAC,gIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;8DACP,6VAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6VAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACX,GAAG,KAAK;;;;;;;;;;;8DAGb,6VAAC,gIAAA,CAAA,kBAAe;;wDAAC;wDACX,sBAAsB,KAAK;wDAAC;;;;;;;8DAElC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAOpB,6VAAC,gIAAA,CAAA,YAAS;oCACR,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;8DACP,6VAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6VAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,6VAAC,oIAAA,CAAA,WAAQ;wDACP,aAAY;wDACZ,WAAU;wDACV,MAAM;wDACL,GAAG,KAAK;;;;;;;;;;;8DAGb,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAMlB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,eAAe,WAAW;;;;;;wCAE5B,0BACC,6VAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6VAAC,kJAAA,CAAA,gBAAa;gBACZ,SAAS;gBACT,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,eAAe;oBACf,oBAAoB;gBACtB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 3931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/payment-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';\nimport { PaymentForm } from './payment-form';\nimport { Bill, Payment } from '@/types/clinic';\n\ninterface PaymentDialogProps {\n  bill: Bill | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess?: (payment: Payment) => void;\n}\n\nexport function PaymentDialog({ bill, isOpen, onClose, onSuccess }: PaymentDialogProps) {\n  const handleSuccess = (payment: Payment) => {\n    if (onSuccess) {\n      onSuccess(payment);\n    }\n    onClose();\n  };\n\n  if (!bill) {\n    return null;\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle>处理支付 - {bill.billNumber}</DialogTitle>\n        </DialogHeader>\n        <PaymentForm\n          bill={bill}\n          onSuccess={handleSuccess}\n          onCancel={onClose}\n          isOpen={true}\n        />\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaO,SAAS,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAsB;IACpF,MAAM,gBAAgB,CAAC;QACrB,IAAI,WAAW;YACb,UAAU;QACZ;QACA;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6VAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6VAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6VAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,6VAAC,kIAAA,CAAA,cAAW;;4BAAC;4BAAQ,KAAK,UAAU;;;;;;;;;;;;8BAEtC,6VAAC,gJAAA,CAAA,cAAW;oBACV,MAAM;oBACN,WAAW;oBACX,UAAU;oBACV,QAAQ;;;;;;;;;;;;;;;;;AAKlB", "debugId": null}}, {"offset": {"line": 4001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/bill-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useForm, useFieldArray } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { billFormSchema, BillFormData } from '@/lib/validation/billing-schemas';\nimport { FormValidator, ValidationResult } from '@/lib/validation/validation-utils';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Separator } from '@/components/ui/separator';\nimport { \n  IconPlus, \n  IconTrash, \n  IconReceipt,\n  IconX,\n  IconCalculator,\n  IconUser,\n  IconCalendar\n} from '@tabler/icons-react';\nimport { Bill, Patient, Appointment, Treatment, BillItem } from '@/types/clinic';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { toast } from 'sonner';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Remove the local schemas since we're now using the centralized ones\n\ninterface BillFormProps {\n  bill?: Bill;\n  patients?: Patient[];\n  appointments?: Appointment[];\n  treatments?: Treatment[];\n  onSuccess?: (bill: Bill) => void;\n  onCancel?: () => void;\n  isOpen?: boolean;\n}\n\n// Bill type options\nconst billTypes = [\n  { value: 'treatment', label: '治疗账单' },\n  { value: 'consultation', label: '咨询账单' },\n  { value: 'deposit', label: '押金账单' },\n  { value: 'additional', label: '补充账单' },\n];\n\n// Item type options\nconst itemTypes = [\n  { value: 'treatment', label: '治疗项目' },\n  { value: 'consultation', label: '咨询服务' },\n  { value: 'material', label: '材料费用' },\n  { value: 'service', label: '其他服务' },\n];\n\nexport function BillForm({\n  bill,\n  patients = [],\n  appointments = [],\n  treatments = [],\n  onSuccess,\n  onCancel,\n  isOpen = true\n}: BillFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [calculatedTotals, setCalculatedTotals] = useState({\n    subtotal: 0,\n    totalAmount: 0,\n  });\n  const [formValidator] = useState(() => new FormValidator(billFormSchema));\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n\n  const isEditing = !!bill;\n\n  const form = useForm<BillFormData>({\n    resolver: zodResolver(billFormSchema),\n    defaultValues: {\n      patient: bill?.patientId ? String(bill.patientId) : '',\n      appointment: bill?.appointmentId ? String(bill.appointmentId) : '',\n      treatment: bill?.treatmentId ? String(bill.treatmentId) : '',\n      billType: bill?.billType || 'treatment',\n      description: bill?.description || '',\n      notes: bill?.notes || '',\n      dueDate: bill?.dueDate ? new Date(bill.dueDate).toISOString().split('T')[0] : '',\n      discountAmount: bill?.discountAmount || 0,\n      taxAmount: bill?.taxAmount || 0,\n      items: bill?.items?.map(item => ({\n        itemType: item.itemType,\n        itemName: item.itemName,\n        description: item.description || '',\n        quantity: item.quantity,\n        unitPrice: item.unitPrice,\n        discountRate: item.discountRate || 0,\n      })) || [\n        {\n          itemType: 'treatment' as const,\n          itemName: '',\n          description: '',\n          quantity: 1,\n          unitPrice: 0,\n          discountRate: 0,\n        }\n      ],\n    },\n  });\n\n  const { fields, append, remove } = useFieldArray({\n    control: form.control,\n    name: 'items',\n  });\n\n  // Watch form values for real-time calculation\n  const watchedItems = form.watch('items');\n  const watchedDiscountAmount = form.watch('discountAmount');\n  const watchedTaxAmount = form.watch('taxAmount');\n\n  // Calculate totals whenever items change\n  useEffect(() => {\n    const subtotal = watchedItems.reduce((sum, item) => {\n      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n      const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n      return sum + (itemTotal - itemDiscount);\n    }, 0);\n\n    const totalAmount = billingUtils.calculateBillTotal(\n      subtotal,\n      watchedDiscountAmount || 0,\n      watchedTaxAmount || 0\n    );\n\n    setCalculatedTotals({ subtotal, totalAmount });\n  }, [watchedItems, watchedDiscountAmount, watchedTaxAmount]);\n\n  const onSubmit = async (data: BillFormData) => {\n    try {\n      setIsSubmitting(true);\n\n      const billData = {\n        patient: parseInt(data.patient),\n        appointment: data.appointment && data.appointment !== 'none' ? parseInt(data.appointment) : undefined,\n        treatment: data.treatment && data.treatment !== 'none' ? parseInt(data.treatment) : undefined,\n        billType: data.billType,\n        subtotal: calculatedTotals.subtotal,\n        discountAmount: data.discountAmount || 0,\n        taxAmount: data.taxAmount || 0,\n        totalAmount: calculatedTotals.totalAmount,\n        description: data.description,\n        notes: data.notes || undefined,\n        dueDate: data.dueDate,\n        items: data.items.map(item => ({\n          itemType: item.itemType,\n          itemName: item.itemName,\n          description: item.description || undefined,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          discountRate: item.discountRate || 0,\n        })),\n      };\n\n      let result: Bill;\n      if (isEditing && bill) {\n        result = await billsAPI.updateBill(bill.id, billData);\n        billingNotifications.bill.updated(result);\n      } else {\n        result = await billsAPI.createBill(billData);\n        billingNotifications.bill.created(result);\n      }\n\n      if (onSuccess) {\n        onSuccess(result);\n      }\n\n      if (!isEditing) {\n        form.reset();\n      }\n      \n    } catch (error) {\n      console.error('Bill operation failed:', error);\n      const errorMessage = error instanceof BillingAPIError\n        ? error.message\n        : undefined;\n\n      if (isEditing) {\n        billingNotifications.bill.updateError(errorMessage);\n      } else {\n        billingNotifications.bill.createError(errorMessage);\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const addItem = () => {\n    append({\n      itemType: 'treatment',\n      itemName: '',\n      description: '',\n      quantity: 1,\n      unitPrice: 0,\n      discountRate: 0,\n    });\n  };\n\n  const removeItem = (index: number) => {\n    if (fields.length > 1) {\n      remove(index);\n    } else {\n      billingNotifications.validation.confirmAction('至少需要保留一个账单项目');\n    }\n  };\n\n  if (!isOpen) {\n    return null;\n  }\n\n  return (\n    <Card className=\"w-full max-w-4xl mx-auto\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <IconReceipt className=\"h-5 w-5\" />\n              {isEditing ? '编辑账单' : '创建账单'}\n            </CardTitle>\n            <CardDescription>\n              {isEditing ? `编辑账单 ${bill?.billNumber}` : '创建新的账单'}\n            </CardDescription>\n          </div>\n          {onCancel && (\n            <Button variant=\"ghost\" size=\"sm\" onClick={onCancel}>\n              <IconX className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\">\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {/* Patient Selection */}\n              <FormField\n                control={form.control}\n                name=\"patient\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"flex items-center gap-2\">\n                      <IconUser className=\"h-4 w-4\" />\n                      患者\n                    </FormLabel>\n                    <FormControl>\n                      <Select value={field.value} onValueChange={field.onChange}>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"选择患者\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {patients.map((patient) => (\n                            <SelectItem key={patient.id} value={String(patient.id)}>\n                              {patient.fullName} - {patient.phone}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Bill Type */}\n              <FormField\n                control={form.control}\n                name=\"billType\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>账单类型</FormLabel>\n                    <FormControl>\n                      <Select value={field.value} onValueChange={field.onChange}>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"选择账单类型\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {billTypes.map((type) => (\n                            <SelectItem key={type.value} value={type.value}>\n                              {type.label}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Appointment (Optional) */}\n              <FormField\n                control={form.control}\n                name=\"appointment\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>关联预约 (可选)</FormLabel>\n                    <FormControl>\n                      <Select value={field.value || ''} onValueChange={field.onChange}>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"选择预约\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          <SelectItem value=\"none\">无关联预约</SelectItem>\n                          {appointments.map((appointment) => (\n                            <SelectItem key={appointment.id} value={appointment.id}>\n                              {new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')} - \n                              {typeof appointment.treatment === 'object' \n                                ? appointment.treatment.name \n                                : '未知治疗'}\n                            </SelectItem>\n                          ))}\n                        </SelectContent>\n                      </Select>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Due Date */}\n              <FormField\n                control={form.control}\n                name=\"dueDate\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel className=\"flex items-center gap-2\">\n                      <IconCalendar className=\"h-4 w-4\" />\n                      到期日期\n                    </FormLabel>\n                    <FormControl>\n                      <Input type=\"date\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Description */}\n            <FormField\n              control={form.control}\n              name=\"description\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>账单描述</FormLabel>\n                  <FormControl>\n                    <Input\n                      placeholder=\"输入账单描述...\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            {/* Notes */}\n            <FormField\n              control={form.control}\n              name=\"notes\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>备注 (可选)</FormLabel>\n                  <FormControl>\n                    <Textarea\n                      placeholder=\"账单相关备注...\"\n                      className=\"resize-none\"\n                      rows={3}\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            <Separator />\n\n            {/* Bill Items Section */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold\">账单项目</h3>\n                <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={addItem}>\n                  <IconPlus className=\"h-4 w-4 mr-2\" />\n                  添加项目\n                </Button>\n              </div>\n\n              {/* Bill Items List */}\n              <div className=\"space-y-4\">\n                {fields.map((field, index) => (\n                  <Card key={field.id} className=\"p-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4 items-end\">\n                      {/* Item Type */}\n                      <FormField\n                        control={form.control}\n                        name={`items.${index}.itemType`}\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>类型</FormLabel>\n                            <FormControl>\n                              <Select value={field.value} onValueChange={field.onChange}>\n                                <SelectTrigger>\n                                  <SelectValue />\n                                </SelectTrigger>\n                                <SelectContent>\n                                  {itemTypes.map((type) => (\n                                    <SelectItem key={type.value} value={type.value}>\n                                      {type.label}\n                                    </SelectItem>\n                                  ))}\n                                </SelectContent>\n                              </Select>\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n\n                      {/* Item Name */}\n                      <FormField\n                        control={form.control}\n                        name={`items.${index}.itemName`}\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>项目名称</FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"项目名称\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n\n                      {/* Quantity */}\n                      <FormField\n                        control={form.control}\n                        name={`items.${index}.quantity`}\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>数量</FormLabel>\n                            <FormControl>\n                              <Input\n                                type=\"number\"\n                                step=\"0.01\"\n                                min=\"0.01\"\n                                placeholder=\"1\"\n                                {...field}\n                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}\n                              />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n\n                      {/* Unit Price */}\n                      <FormField\n                        control={form.control}\n                        name={`items.${index}.unitPrice`}\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>单价</FormLabel>\n                            <FormControl>\n                              <Input\n                                type=\"number\"\n                                step=\"0.01\"\n                                min=\"0\"\n                                placeholder=\"0.00\"\n                                {...field}\n                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}\n                              />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n\n                      {/* Discount Rate */}\n                      <FormField\n                        control={form.control}\n                        name={`items.${index}.discountRate`}\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>折扣率 (%)</FormLabel>\n                            <FormControl>\n                              <Input\n                                type=\"number\"\n                                step=\"0.1\"\n                                min=\"0\"\n                                max=\"100\"\n                                placeholder=\"0\"\n                                {...field}\n                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}\n                              />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n\n                      {/* Remove Button */}\n                      <div className=\"flex items-center\">\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => removeItem(index)}\n                          disabled={fields.length <= 1}\n                        >\n                          <IconTrash className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n\n                    {/* Item Description */}\n                    <div className=\"mt-4\">\n                      <FormField\n                        control={form.control}\n                        name={`items.${index}.description`}\n                        render={({ field }) => (\n                          <FormItem>\n                            <FormLabel>项目描述 (可选)</FormLabel>\n                            <FormControl>\n                              <Textarea\n                                placeholder=\"项目详细描述...\"\n                                className=\"resize-none\"\n                                rows={2}\n                                {...field}\n                              />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>\n                        )}\n                      />\n                    </div>\n\n                    {/* Item Total Display */}\n                    <div className=\"mt-2 text-right\">\n                      <span className=\"text-sm text-muted-foreground\">\n                        小计: {billingUtils.formatCurrency(\n                          (() => {\n                            const item = watchedItems[index];\n                            if (!item) return 0;\n                            const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n                            const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n                            return itemTotal - itemDiscount;\n                          })()\n                        )}\n                      </span>\n                    </div>\n                  </Card>\n                ))}\n              </div>\n            </div>\n\n            <Separator />\n\n            {/* Totals Section */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {/* Discount Amount */}\n              <FormField\n                control={form.control}\n                name=\"discountAmount\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>额外折扣金额</FormLabel>\n                    <FormControl>\n                      <Input\n                        type=\"number\"\n                        step=\"0.01\"\n                        min=\"0\"\n                        placeholder=\"0.00\"\n                        {...field}\n                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}\n                      />\n                    </FormControl>\n                    <FormDescription>\n                      在项目折扣基础上的额外折扣\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Tax Amount */}\n              <FormField\n                control={form.control}\n                name=\"taxAmount\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>税费金额</FormLabel>\n                    <FormControl>\n                      <Input\n                        type=\"number\"\n                        step=\"0.01\"\n                        min=\"0\"\n                        placeholder=\"0.00\"\n                        {...field}\n                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}\n                      />\n                    </FormControl>\n                    <FormDescription>\n                      需要添加的税费金额\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Total Display */}\n              <div className=\"space-y-2\">\n                <Label>账单总计</Label>\n                <div className=\"bg-muted/50 rounded-lg p-3 space-y-1\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span>项目小计:</span>\n                    <span>{billingUtils.formatCurrency(calculatedTotals.subtotal)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>额外折扣:</span>\n                    <span>-{billingUtils.formatCurrency(watchedDiscountAmount || 0)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>税费:</span>\n                    <span>+{billingUtils.formatCurrency(watchedTaxAmount || 0)}</span>\n                  </div>\n                  <Separator />\n                  <div className=\"flex justify-between font-semibold\">\n                    <span>总金额:</span>\n                    <span className=\"text-lg\">\n                      {billingUtils.formatCurrency(calculatedTotals.totalAmount)}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"flex-1\"\n              >\n                {isSubmitting\n                  ? (isEditing ? '更新中...' : '创建中...')\n                  : (isEditing ? '更新账单' : '创建账单')\n                }\n              </Button>\n              {onCancel && (\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={onCancel}\n                  disabled={isSubmitting}\n                >\n                  取消\n                </Button>\n              )}\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AA5BA;;;;;;;;;;;;;;;;;;AA0CA,oBAAoB;AACpB,MAAM,YAAY;IAChB;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAgB,OAAO;IAAO;IACvC;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAc,OAAO;IAAO;CACtC;AAED,oBAAoB;AACpB,MAAM,YAAY;IAChB;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAgB,OAAO;IAAO;IACvC;QAAE,OAAO;QAAY,OAAO;IAAO;IACnC;QAAE,OAAO;QAAW,OAAO;IAAO;CACnC;AAEM,SAAS,SAAS,EACvB,IAAI,EACJ,WAAW,EAAE,EACb,eAAe,EAAE,EACjB,aAAa,EAAE,EACf,SAAS,EACT,QAAQ,EACR,SAAS,IAAI,EACC;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,UAAU;QACV,aAAa;IACf;IACA,MAAM,CAAC,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,IAAI,+IAAA,CAAA,gBAAa,CAAC,8IAAA,CAAA,iBAAc;IACvE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,MAAM,YAAY,CAAC,CAAC;IAEpB,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAAgB;QACjC,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE,8IAAA,CAAA,iBAAc;QACpC,eAAe;YACb,SAAS,MAAM,YAAY,OAAO,KAAK,SAAS,IAAI;YACpD,aAAa,MAAM,gBAAgB,OAAO,KAAK,aAAa,IAAI;YAChE,WAAW,MAAM,cAAc,OAAO,KAAK,WAAW,IAAI;YAC1D,UAAU,MAAM,YAAY;YAC5B,aAAa,MAAM,eAAe;YAClC,OAAO,MAAM,SAAS;YACtB,SAAS,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YAC9E,gBAAgB,MAAM,kBAAkB;YACxC,WAAW,MAAM,aAAa;YAC9B,OAAO,MAAM,OAAO,IAAI,CAAA,OAAQ,CAAC;oBAC/B,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,aAAa,KAAK,WAAW,IAAI;oBACjC,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS;oBACzB,cAAc,KAAK,YAAY,IAAI;gBACrC,CAAC,MAAM;gBACL;oBACE,UAAU;oBACV,UAAU;oBACV,aAAa;oBACb,UAAU;oBACV,WAAW;oBACX,cAAc;gBAChB;aACD;QACH;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uPAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C,SAAS,KAAK,OAAO;QACrB,MAAM;IACR;IAEA,8CAA8C;IAC9C,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,wBAAwB,KAAK,KAAK,CAAC;IACzC,MAAM,mBAAmB,KAAK,KAAK,CAAC;IAEpC,yCAAyC;IACzC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,aAAa,MAAM,CAAC,CAAC,KAAK;YACzC,MAAM,YAAY,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC;YAC7D,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;YAChE,OAAO,MAAM,CAAC,YAAY,YAAY;QACxC,GAAG;QAEH,MAAM,cAAc,4HAAA,CAAA,eAAY,CAAC,kBAAkB,CACjD,UACA,yBAAyB,GACzB,oBAAoB;QAGtB,oBAAoB;YAAE;YAAU;QAAY;IAC9C,GAAG;QAAC;QAAc;QAAuB;KAAiB;IAE1D,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,gBAAgB;YAEhB,MAAM,WAAW;gBACf,SAAS,SAAS,KAAK,OAAO;gBAC9B,aAAa,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,SAAS,SAAS,KAAK,WAAW,IAAI;gBAC5F,WAAW,KAAK,SAAS,IAAI,KAAK,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,IAAI;gBACpF,UAAU,KAAK,QAAQ;gBACvB,UAAU,iBAAiB,QAAQ;gBACnC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,WAAW,KAAK,SAAS,IAAI;gBAC7B,aAAa,iBAAiB,WAAW;gBACzC,aAAa,KAAK,WAAW;gBAC7B,OAAO,KAAK,KAAK,IAAI;gBACrB,SAAS,KAAK,OAAO;gBACrB,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,UAAU,KAAK,QAAQ;wBACvB,aAAa,KAAK,WAAW,IAAI;wBACjC,UAAU,KAAK,QAAQ;wBACvB,WAAW,KAAK,SAAS;wBACzB,cAAc,KAAK,YAAY,IAAI;oBACrC,CAAC;YACH;YAEA,IAAI;YACJ,IAAI,aAAa,MAAM;gBACrB,SAAS,MAAM,4HAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE;gBAC5C,sIAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;YACpC,OAAO;gBACL,SAAS,MAAM,4HAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;gBACnC,sIAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;YACpC;YAEA,IAAI,WAAW;gBACb,UAAU;YACZ;YAEA,IAAI,CAAC,WAAW;gBACd,KAAK,KAAK;YACZ;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAAe,iBAAiB,4HAAA,CAAA,kBAAe,GACjD,MAAM,OAAO,GACb;YAEJ,IAAI,WAAW;gBACb,sIAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC;YACxC,OAAO;gBACL,sIAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC;YACxC;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,UAAU;QACd,OAAO;YACL,UAAU;YACV,UAAU;YACV,aAAa;YACb,UAAU;YACV,WAAW;YACX,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO;QACT,OAAO;YACL,sIAAA,CAAA,uBAAoB,CAAC,UAAU,CAAC,aAAa,CAAC;QAChD;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,qBACE,6VAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6VAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;;8CACC,6VAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6VAAC,0TAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,YAAY,SAAS;;;;;;;8CAExB,6VAAC,gIAAA,CAAA,kBAAe;8CACb,YAAY,CAAC,KAAK,EAAE,MAAM,YAAY,GAAG;;;;;;;;;;;;wBAG7C,0BACC,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;sCACzC,cAAA,6VAAC,8SAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMzB,6VAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6VAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,6VAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAW,WAAU;;0CAErD,6VAAC;gCAAI,WAAU;;kDAEb,6VAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;kEACP,6VAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6VAAC,oTAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGlC,6VAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,6VAAC,kIAAA,CAAA,SAAM;4DAAC,OAAO,MAAM,KAAK;4DAAE,eAAe,MAAM,QAAQ;;8EACvD,6VAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6VAAC,kIAAA,CAAA,gBAAa;8EACX,SAAS,GAAG,CAAC,CAAC,wBACb,6VAAC,kIAAA,CAAA,aAAU;4EAAkB,OAAO,OAAO,QAAQ,EAAE;;gFAClD,QAAQ,QAAQ;gFAAC;gFAAI,QAAQ,KAAK;;2EADpB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;kEAOnC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,6VAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;kEACP,6VAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6VAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,6VAAC,kIAAA,CAAA,SAAM;4DAAC,OAAO,MAAM,KAAK;4DAAE,eAAe,MAAM,QAAQ;;8EACvD,6VAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6VAAC,kIAAA,CAAA,gBAAa;8EACX,UAAU,GAAG,CAAC,CAAC,qBACd,6VAAC,kIAAA,CAAA,aAAU;4EAAkB,OAAO,KAAK,KAAK;sFAC3C,KAAK,KAAK;2EADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;kEAOnC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,6VAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;kEACP,6VAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6VAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,6VAAC,kIAAA,CAAA,SAAM;4DAAC,OAAO,MAAM,KAAK,IAAI;4DAAI,eAAe,MAAM,QAAQ;;8EAC7D,6VAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6VAAC,kIAAA,CAAA,gBAAa;;sFACZ,6VAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;wEACxB,aAAa,GAAG,CAAC,CAAC,4BACjB,6VAAC,kIAAA,CAAA,aAAU;gFAAsB,OAAO,YAAY,EAAE;;oFACnD,IAAI,KAAK,YAAY,eAAe,EAAE,kBAAkB,CAAC;oFAAS;oFAClE,OAAO,YAAY,SAAS,KAAK,WAC9B,YAAY,SAAS,CAAC,IAAI,GAC1B;;+EAJW,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;kEAUvC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,6VAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;kEACP,6VAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6VAAC,4TAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGtC,6VAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;4DAAC,MAAK;4DAAQ,GAAG,KAAK;;;;;;;;;;;kEAE9B,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAOpB,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0DACP,6VAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACX,GAAG,KAAK;;;;;;;;;;;0DAGb,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAMlB,6VAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0DACP,6VAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6VAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,6VAAC,oIAAA,CAAA,WAAQ;oDACP,aAAY;oDACZ,WAAU;oDACV,MAAM;oDACL,GAAG,KAAK;;;;;;;;;;;0DAGb,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAKlB,6VAAC,qIAAA,CAAA,YAAS;;;;;0CAGV,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAG,WAAU;0DAAwB;;;;;;0DACtC,6VAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAQ;gDAAU,MAAK;gDAAK,SAAS;;kEACzD,6VAAC,oTAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAMzC,6VAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6VAAC,gIAAA,CAAA,OAAI;gDAAgB,WAAU;;kEAC7B,6VAAC;wDAAI,WAAU;;0EAEb,6VAAC,gIAAA,CAAA,YAAS;gEACR,SAAS,KAAK,OAAO;gEACrB,MAAM,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC;gEAC/B,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0FACP,6VAAC,gIAAA,CAAA,YAAS;0FAAC;;;;;;0FACX,6VAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,6VAAC,kIAAA,CAAA,SAAM;oFAAC,OAAO,MAAM,KAAK;oFAAE,eAAe,MAAM,QAAQ;;sGACvD,6VAAC,kIAAA,CAAA,gBAAa;sGACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6VAAC,kIAAA,CAAA,gBAAa;sGACX,UAAU,GAAG,CAAC,CAAC,qBACd,6VAAC,kIAAA,CAAA,aAAU;oGAAkB,OAAO,KAAK,KAAK;8GAC3C,KAAK,KAAK;mGADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0FAOnC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0EAMlB,6VAAC,gIAAA,CAAA,YAAS;gEACR,SAAS,KAAK,OAAO;gEACrB,MAAM,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC;gEAC/B,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0FACP,6VAAC,gIAAA,CAAA,YAAS;0FAAC;;;;;;0FACX,6VAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oFAAC,aAAY;oFAAQ,GAAG,KAAK;;;;;;;;;;;0FAErC,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0EAMlB,6VAAC,gIAAA,CAAA,YAAS;gEACR,SAAS,KAAK,OAAO;gEACrB,MAAM,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC;gEAC/B,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0FACP,6VAAC,gIAAA,CAAA,YAAS;0FAAC;;;;;;0FACX,6VAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,MAAK;oFACL,KAAI;oFACJ,aAAY;oFACX,GAAG,KAAK;oFACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;0FAGlE,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0EAMlB,6VAAC,gIAAA,CAAA,YAAS;gEACR,SAAS,KAAK,OAAO;gEACrB,MAAM,CAAC,MAAM,EAAE,MAAM,UAAU,CAAC;gEAChC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0FACP,6VAAC,gIAAA,CAAA,YAAS;0FAAC;;;;;;0FACX,6VAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,MAAK;oFACL,KAAI;oFACJ,aAAY;oFACX,GAAG,KAAK;oFACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;0FAGlE,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0EAMlB,6VAAC,gIAAA,CAAA,YAAS;gEACR,SAAS,KAAK,OAAO;gEACrB,MAAM,CAAC,MAAM,EAAE,MAAM,aAAa,CAAC;gEACnC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;0FACP,6VAAC,gIAAA,CAAA,YAAS;0FAAC;;;;;;0FACX,6VAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,MAAK;oFACL,KAAI;oFACJ,KAAI;oFACJ,aAAY;oFACX,GAAG,KAAK;oFACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;0FAGlE,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0EAMlB,6VAAC;gEAAI,WAAU;0EACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,WAAW;oEAC1B,UAAU,OAAO,MAAM,IAAI;8EAE3B,cAAA,6VAAC,sTAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kEAM3B,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC,gIAAA,CAAA,YAAS;4DACR,SAAS,KAAK,OAAO;4DACrB,MAAM,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC;4DAClC,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;sFACP,6VAAC,gIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,6VAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,6VAAC,oIAAA,CAAA,WAAQ;gFACP,aAAY;gFACZ,WAAU;gFACV,MAAM;gFACL,GAAG,KAAK;;;;;;;;;;;sFAGb,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;kEAOpB,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC;4DAAK,WAAU;;gEAAgC;gEACzC,4HAAA,CAAA,eAAY,CAAC,cAAc,CAC9B,CAAC;oEACC,MAAM,OAAO,YAAY,CAAC,MAAM;oEAChC,IAAI,CAAC,MAAM,OAAO;oEAClB,MAAM,YAAY,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC;oEAC7D,MAAM,eAAe,YAAY,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI,GAAG;oEAChE,OAAO,YAAY;gEACrB,CAAC;;;;;;;;;;;;;+CA5JE,MAAM,EAAE;;;;;;;;;;;;;;;;0CAqKzB,6VAAC,qIAAA,CAAA,YAAS;;;;;0CAGV,6VAAC;gCAAI,WAAU;;kDAEb,6VAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;kEACP,6VAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6VAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,MAAK;4DACL,KAAI;4DACJ,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;kEAGlE,6VAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;kEAGjB,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,6VAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6VAAC,gIAAA,CAAA,WAAQ;;kEACP,6VAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6VAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,6VAAC,iIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,MAAK;4DACL,KAAI;4DACJ,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;kEAGlE,6VAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;kEAGjB,6VAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;0EAAK;;;;;;0EACN,6VAAC;0EAAM,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,iBAAiB,QAAQ;;;;;;;;;;;;kEAE9D,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;0EAAK;;;;;;0EACN,6VAAC;;oEAAK;oEAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,yBAAyB;;;;;;;;;;;;;kEAE/D,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;0EAAK;;;;;;0EACN,6VAAC;;oEAAK;oEAAE,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,oBAAoB;;;;;;;;;;;;;kEAE1D,6VAAC,qIAAA,CAAA,YAAS;;;;;kEACV,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;0EAAK;;;;;;0EACN,6VAAC;gEAAK,WAAU;0EACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQnE,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,eACI,YAAY,WAAW,WACvB,YAAY,SAAS;;;;;;oCAG3B,0BACC,6VAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 5413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/bill-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';\nimport { BillForm } from './bill-form';\nimport { Bill, Patient, Appointment, Treatment } from '@/types/clinic';\nimport { toast } from 'sonner';\n\ninterface BillDialogProps {\n  bill?: Bill | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess?: (bill: Bill) => void;\n}\n\nexport function BillDialog({ bill, isOpen, onClose, onSuccess }: BillDialogProps) {\n  const [patients, setPatients] = useState<Patient[]>([]);\n  const [appointments, setAppointments] = useState<Appointment[]>([]);\n  const [treatments, setTreatments] = useState<Treatment[]>([]);\n  const [loading, setLoading] = useState(false);\n\n  // Fetch required data when dialog opens\n  useEffect(() => {\n    if (isOpen) {\n      fetchRequiredData();\n    }\n  }, [isOpen]);\n\n  const fetchRequiredData = async () => {\n    try {\n      setLoading(true);\n      \n      // Fetch patients, appointments, and treatments\n      // These would typically come from your API\n      const [patientsResponse, appointmentsResponse, treatmentsResponse] = await Promise.all([\n        fetch('/api/patients').then(res => res.json()),\n        fetch('/api/appointments').then(res => res.json()),\n        fetch('/api/treatments').then(res => res.json()),\n      ]);\n\n      setPatients(patientsResponse.docs || []);\n      setAppointments(appointmentsResponse.docs || []);\n      setTreatments(treatmentsResponse.docs || []);\n    } catch (error) {\n      console.error('Failed to fetch required data:', error);\n      toast.error('加载数据失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSuccess = (newBill: Bill) => {\n    if (onSuccess) {\n      onSuccess(newBill);\n    }\n    onClose();\n  };\n\n  const isEditing = !!bill;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-5xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle>\n            {isEditing ? `编辑账单 - ${bill?.billNumber}` : '创建新账单'}\n          </DialogTitle>\n        </DialogHeader>\n        \n        {loading ? (\n          <div className=\"flex items-center justify-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n            <span className=\"ml-2 text-muted-foreground\">加载数据中...</span>\n          </div>\n        ) : (\n          <BillForm\n            bill={bill || undefined}\n            patients={patients}\n            appointments={appointments}\n            treatments={treatments}\n            onSuccess={handleSuccess}\n            onCancel={onClose}\n            isOpen={true}\n          />\n        )}\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAeO,SAAS,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAmB;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YAEX,+CAA+C;YAC/C,2CAA2C;YAC3C,MAAM,CAAC,kBAAkB,sBAAsB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrF,MAAM,iBAAiB,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;gBAC3C,MAAM,qBAAqB,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;gBAC/C,MAAM,mBAAmB,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;aAC9C;YAED,YAAY,iBAAiB,IAAI,IAAI,EAAE;YACvC,gBAAgB,qBAAqB,IAAI,IAAI,EAAE;YAC/C,cAAc,mBAAmB,IAAI,IAAI,EAAE;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,WAAW;YACb,UAAU;QACZ;QACA;IACF;IAEA,MAAM,YAAY,CAAC,CAAC;IAEpB,qBACE,6VAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6VAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6VAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,6VAAC,kIAAA,CAAA,cAAW;kCACT,YAAY,CAAC,OAAO,EAAE,MAAM,YAAY,GAAG;;;;;;;;;;;gBAI/C,wBACC,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;;;;;sCACf,6VAAC;4BAAK,WAAU;sCAA6B;;;;;;;;;;;yCAG/C,6VAAC,6IAAA,CAAA,WAAQ;oBACP,MAAM,QAAQ;oBACd,UAAU;oBACV,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,UAAU;oBACV,QAAQ;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 5540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst alertVariants = cva(\r\n  'relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-card text-card-foreground',\r\n        destructive:\r\n          'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot='alert'\r\n      role='alert'\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-title'\r\n      className={cn(\r\n        'col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-description'\r\n      className={cn(\r\n        'text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription };\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6VAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/bill-status-manager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Badge } from '@/components/ui/badge';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { \n  IconEdit, \n  IconCheck, \n  IconX, \n  IconAlertTriangle,\n  IconMail,\n  IconFileText,\n  IconCreditCard\n} from '@tabler/icons-react';\nimport { Bill } from '@/types/clinic';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { toast } from 'sonner';\n\ninterface BillStatusManagerProps {\n  bill: Bill;\n  onStatusUpdate?: (updatedBill: Bill) => void;\n  trigger?: React.ReactNode;\n}\n\n// Define valid status transitions\nconst statusTransitions: Record<string, string[]> = {\n  draft: ['sent', 'cancelled'],\n  sent: ['confirmed', 'cancelled'],\n  confirmed: ['paid', 'cancelled'],\n  paid: [], // Final status - no transitions allowed\n  cancelled: [], // Final status - no transitions allowed\n};\n\n// Status display configuration\nconst statusConfig = {\n  draft: {\n    label: '草稿',\n    color: 'bg-gray-100 text-gray-800',\n    icon: IconFileText,\n    description: '账单正在编辑中',\n  },\n  sent: {\n    label: '已发送',\n    color: 'bg-blue-100 text-blue-800',\n    icon: IconMail,\n    description: '账单已发送给患者',\n  },\n  confirmed: {\n    label: '已确认',\n    color: 'bg-yellow-100 text-yellow-800',\n    icon: IconCheck,\n    description: '患者已确认账单',\n  },\n  paid: {\n    label: '已支付',\n    color: 'bg-green-100 text-green-800',\n    icon: IconCreditCard,\n    description: '账单已完全支付',\n  },\n  cancelled: {\n    label: '已取消',\n    color: 'bg-red-100 text-red-800',\n    icon: IconX,\n    description: '账单已取消',\n  },\n};\n\nexport function BillStatusManager({ bill, onStatusUpdate, trigger }: BillStatusManagerProps) {\n  const { hasPermission } = useRole();\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedStatus, setSelectedStatus] = useState(bill.status);\n  const [notes, setNotes] = useState('');\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  const currentStatusConfig = statusConfig[bill.status];\n  const availableTransitions = statusTransitions[bill.status] || [];\n  const canUpdateStatus = hasPermission('canEditBills') && availableTransitions.length > 0;\n\n  const handleStatusUpdate = async () => {\n    if (selectedStatus === bill.status) {\n      toast.warning('请选择不同的状态');\n      return;\n    }\n\n    try {\n      setIsUpdating(true);\n\n      // Validate transition\n      if (!availableTransitions.includes(selectedStatus)) {\n        toast.error('无效的状态转换');\n        return;\n      }\n\n      // Special validation for paid status\n      if (selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0) {\n        toast.error('账单还有未支付金额，无法标记为已支付');\n        return;\n      }\n\n      const updateData: Partial<Bill> = {\n        status: selectedStatus as Bill['status'],\n      };\n\n      // Add notes if provided\n      if (notes.trim()) {\n        updateData.notes = bill.notes \n          ? `${bill.notes}\\n\\n[状态更新] ${notes.trim()}`\n          : `[状态更新] ${notes.trim()}`;\n      }\n\n      const updatedBill = await billsAPI.updateBill(bill.id, updateData);\n\n      toast.success(`账单状态已更新为: ${statusConfig[selectedStatus].label}`);\n      \n      if (onStatusUpdate) {\n        onStatusUpdate(updatedBill);\n      }\n\n      setIsOpen(false);\n      setNotes('');\n      \n    } catch (error) {\n      console.error('Failed to update bill status:', error);\n      const errorMessage = error instanceof BillingAPIError \n        ? error.message \n        : '状态更新失败，请稍后重试';\n      toast.error(errorMessage);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const getStatusWarning = (status: string) => {\n    switch (status) {\n      case 'sent':\n        return '发送账单后，患者将收到账单通知';\n      case 'confirmed':\n        return '确认账单表示患者已同意账单内容';\n      case 'paid':\n        return '标记为已支付前，请确保所有款项已收到';\n      case 'cancelled':\n        return '取消账单后将无法恢复，请谨慎操作';\n      default:\n        return null;\n    }\n  };\n\n  if (!canUpdateStatus) {\n    return (\n      <Badge className={currentStatusConfig.color}>\n        <currentStatusConfig.icon className=\"h-3 w-3 mr-1\" />\n        {currentStatusConfig.label}\n      </Badge>\n    );\n  }\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild>\n        {trigger || (\n          <Button variant=\"outline\" size=\"sm\">\n            <IconEdit className=\"h-4 w-4 mr-2\" />\n            更新状态\n          </Button>\n        )}\n      </DialogTrigger>\n      \n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle>更新账单状态</DialogTitle>\n          <DialogDescription>\n            账单编号: {bill.billNumber}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Current Status */}\n          <div>\n            <Label className=\"text-sm font-medium\">当前状态</Label>\n            <div className=\"mt-1\">\n              <Badge className={currentStatusConfig.color}>\n                <currentStatusConfig.icon className=\"h-3 w-3 mr-1\" />\n                {currentStatusConfig.label}\n              </Badge>\n              <p className=\"text-xs text-muted-foreground mt-1\">\n                {currentStatusConfig.description}\n              </p>\n            </div>\n          </div>\n\n          {/* New Status Selection */}\n          <div>\n            <Label htmlFor=\"status-select\" className=\"text-sm font-medium\">\n              新状态\n            </Label>\n            <Select value={selectedStatus} onValueChange={setSelectedStatus}>\n              <SelectTrigger className=\"mt-1\">\n                <SelectValue placeholder=\"选择新状态\" />\n              </SelectTrigger>\n              <SelectContent>\n                {availableTransitions.map((status) => {\n                  const config = statusConfig[status];\n                  const Icon = config.icon;\n                  return (\n                    <SelectItem key={status} value={status}>\n                      <div className=\"flex items-center gap-2\">\n                        <Icon className=\"h-4 w-4\" />\n                        <span>{config.label}</span>\n                      </div>\n                    </SelectItem>\n                  );\n                })}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Status Warning */}\n          {selectedStatus !== bill.status && getStatusWarning(selectedStatus) && (\n            <Alert>\n              <IconAlertTriangle className=\"h-4 w-4\" />\n              <AlertDescription>\n                {getStatusWarning(selectedStatus)}\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Special validation for paid status */}\n          {selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0 && (\n            <Alert variant=\"destructive\">\n              <IconAlertTriangle className=\"h-4 w-4\" />\n              <AlertDescription>\n                账单还有 {billingUtils.formatCurrency(bill.remainingAmount || 0)} 未支付，\n                无法标记为已支付状态。\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Notes */}\n          <div>\n            <Label htmlFor=\"notes\" className=\"text-sm font-medium\">\n              备注 (可选)\n            </Label>\n            <Textarea\n              id=\"notes\"\n              placeholder=\"添加状态更新的备注...\"\n              value={notes}\n              onChange={(e) => setNotes(e.target.value)}\n              className=\"mt-1 resize-none\"\n              rows={3}\n            />\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex gap-2 pt-2\">\n            <Button\n              onClick={handleStatusUpdate}\n              disabled={\n                isUpdating || \n                selectedStatus === bill.status ||\n                (selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0)\n              }\n              className=\"flex-1\"\n            >\n              {isUpdating ? '更新中...' : '确认更新'}\n            </Button>\n            <Button\n              variant=\"outline\"\n              onClick={() => {\n                setIsOpen(false);\n                setSelectedStatus(bill.status);\n                setNotes('');\n              }}\n              disabled={isUpdating}\n            >\n              取消\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n\n// Export status configuration for use in other components\nexport { statusConfig, statusTransitions };\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAtBA;;;;;;;;;;;;;;AA8BA,kCAAkC;AAClC,MAAM,oBAA8C;IAClD,OAAO;QAAC;QAAQ;KAAY;IAC5B,MAAM;QAAC;QAAa;KAAY;IAChC,WAAW;QAAC;QAAQ;KAAY;IAChC,MAAM,EAAE;IACR,WAAW,EAAE;AACf;AAEA,+BAA+B;AAC/B,MAAM,eAAe;IACnB,OAAO;QACL,OAAO;QACP,OAAO;QACP,MAAM,4TAAA,CAAA,eAAY;QAClB,aAAa;IACf;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,MAAM,oTAAA,CAAA,WAAQ;QACd,aAAa;IACf;IACA,WAAW;QACT,OAAO;QACP,OAAO;QACP,MAAM,sTAAA,CAAA,YAAS;QACf,aAAa;IACf;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,MAAM,gUAAA,CAAA,iBAAc;QACpB,aAAa;IACf;IACA,WAAW;QACT,OAAO;QACP,OAAO;QACP,MAAM,8SAAA,CAAA,QAAK;QACX,aAAa;IACf;AACF;AAEO,SAAS,kBAAkB,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAA0B;IACzF,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,MAAM;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,sBAAsB,YAAY,CAAC,KAAK,MAAM,CAAC;IACrD,MAAM,uBAAuB,iBAAiB,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE;IACjE,MAAM,kBAAkB,cAAc,mBAAmB,qBAAqB,MAAM,GAAG;IAEvF,MAAM,qBAAqB;QACzB,IAAI,mBAAmB,KAAK,MAAM,EAAE;YAClC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF;QAEA,IAAI;YACF,cAAc;YAEd,sBAAsB;YACtB,IAAI,CAAC,qBAAqB,QAAQ,CAAC,iBAAiB;gBAClD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,qCAAqC;YACrC,IAAI,mBAAmB,UAAU,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,GAAG;gBAChE,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,aAA4B;gBAChC,QAAQ;YACV;YAEA,wBAAwB;YACxB,IAAI,MAAM,IAAI,IAAI;gBAChB,WAAW,KAAK,GAAG,KAAK,KAAK,GACzB,GAAG,KAAK,KAAK,CAAC,WAAW,EAAE,MAAM,IAAI,IAAI,GACzC,CAAC,OAAO,EAAE,MAAM,IAAI,IAAI;YAC9B;YAEA,MAAM,cAAc,MAAM,4HAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE;YAEvD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,eAAe,CAAC,KAAK,EAAE;YAE/D,IAAI,gBAAgB;gBAClB,eAAe;YACjB;YAEA,UAAU;YACV,SAAS;QAEX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,eAAe,iBAAiB,4HAAA,CAAA,kBAAe,GACjD,MAAM,OAAO,GACb;YACJ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6VAAC,iIAAA,CAAA,QAAK;YAAC,WAAW,oBAAoB,KAAK;;8BACzC,6VAAC,oBAAoB,IAAI;oBAAC,WAAU;;;;;;gBACnC,oBAAoB,KAAK;;;;;;;IAGhC;IAEA,qBACE,6VAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;;0BAClC,6VAAC,kIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACnB,yBACC,6VAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6VAAC,oTAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAM3C,6VAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,6VAAC,kIAAA,CAAA,eAAY;;0CACX,6VAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;0CACb,6VAAC,kIAAA,CAAA,oBAAiB;;oCAAC;oCACV,KAAK,UAAU;;;;;;;;;;;;;kCAI1B,6VAAC;wBAAI,WAAU;;0CAEb,6VAAC;;kDACC,6VAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,oBAAoB,KAAK;;kEACzC,6VAAC,oBAAoB,IAAI;wDAAC,WAAU;;;;;;oDACnC,oBAAoB,KAAK;;;;;;;0DAE5B,6VAAC;gDAAE,WAAU;0DACV,oBAAoB,WAAW;;;;;;;;;;;;;;;;;;0CAMtC,6VAAC;;kDACC,6VAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAgB,WAAU;kDAAsB;;;;;;kDAG/D,6VAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAgB,eAAe;;0DAC5C,6VAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6VAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6VAAC,kIAAA,CAAA,gBAAa;0DACX,qBAAqB,GAAG,CAAC,CAAC;oDACzB,MAAM,SAAS,YAAY,CAAC,OAAO;oDACnC,MAAM,OAAO,OAAO,IAAI;oDACxB,qBACE,6VAAC,kIAAA,CAAA,aAAU;wDAAc,OAAO;kEAC9B,cAAA,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAK,WAAU;;;;;;8EAChB,6VAAC;8EAAM,OAAO,KAAK;;;;;;;;;;;;uDAHN;;;;;gDAOrB;;;;;;;;;;;;;;;;;;4BAML,mBAAmB,KAAK,MAAM,IAAI,iBAAiB,iCAClD,6VAAC,iIAAA,CAAA,QAAK;;kDACJ,6VAAC,sUAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;kDAC7B,6VAAC,iIAAA,CAAA,mBAAgB;kDACd,iBAAiB;;;;;;;;;;;;4BAMvB,mBAAmB,UAAU,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,mBAC1D,6VAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;kDACb,6VAAC,sUAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;kDAC7B,6VAAC,iIAAA,CAAA,mBAAgB;;4CAAC;4CACV,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,eAAe,IAAI;4CAAG;;;;;;;;;;;;;0CAOnE,6VAAC;;kDACC,6VAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAQ,WAAU;kDAAsB;;;;;;kDAGvD,6VAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,MAAM;;;;;;;;;;;;0CAKV,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UACE,cACA,mBAAmB,KAAK,MAAM,IAC7B,mBAAmB,UAAU,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI;wCAE9D,WAAU;kDAET,aAAa,WAAW;;;;;;kDAE3B,6VAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,UAAU;4CACV,kBAAkB,KAAK,MAAM;4CAC7B,SAAS;wCACX;wCACA,UAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 6102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot='popover' {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot='popover-trigger' {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot='popover-content'\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot='popover-anchor' {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6VAAC,6QAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6VAAC,6QAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6VAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,6VAAC,6QAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6VAAC,6QAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 6171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/bill-filters.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { \n  IconFilter, \n  IconX, \n  IconSearch,\n  IconCalendar,\n  IconCurrencyYuan,\n  IconUser,\n  IconFileText\n} from '@tabler/icons-react';\nimport { billingUtils } from '@/lib/api/billing';\n\nexport interface BillFilterOptions {\n  search?: string;\n  status?: string;\n  billType?: string;\n  patientId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n  amountMin?: number;\n  amountMax?: number;\n}\n\ninterface BillFiltersProps {\n  filters: BillFilterOptions;\n  onFiltersChange: (filters: BillFilterOptions) => void;\n  patients?: Array<{ id: string; fullName: string; phone: string }>;\n  className?: string;\n}\n\nconst billStatuses = [\n  { value: 'draft', label: '草稿' },\n  { value: 'sent', label: '已发送' },\n  { value: 'confirmed', label: '已确认' },\n  { value: 'paid', label: '已支付' },\n  { value: 'cancelled', label: '已取消' },\n];\n\nconst billTypes = [\n  { value: 'treatment', label: '治疗账单' },\n  { value: 'consultation', label: '咨询账单' },\n  { value: 'deposit', label: '押金账单' },\n  { value: 'additional', label: '补充账单' },\n];\n\nexport function BillFilters({ filters, onFiltersChange, patients = [], className }: BillFiltersProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [localFilters, setLocalFilters] = useState<BillFilterOptions>(filters);\n\n  const hasActiveFilters = Object.values(filters).some(value => \n    value !== undefined && value !== '' && value !== null\n  );\n\n  const activeFilterCount = Object.values(filters).filter(value => \n    value !== undefined && value !== '' && value !== null\n  ).length;\n\n  const handleFilterChange = (key: keyof BillFilterOptions, value: any) => {\n    const newFilters = { ...localFilters, [key]: value };\n    setLocalFilters(newFilters);\n  };\n\n  const applyFilters = () => {\n    onFiltersChange(localFilters);\n    setIsOpen(false);\n  };\n\n  const clearFilters = () => {\n    const emptyFilters: BillFilterOptions = {};\n    setLocalFilters(emptyFilters);\n    onFiltersChange(emptyFilters);\n    setIsOpen(false);\n  };\n\n  const clearSingleFilter = (key: keyof BillFilterOptions) => {\n    const newFilters = { ...filters };\n    delete newFilters[key];\n    onFiltersChange(newFilters);\n  };\n\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {/* Search Bar */}\n      <div className=\"flex items-center gap-2\">\n        <div className=\"relative flex-1\">\n          <IconSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n          <Input\n            placeholder=\"搜索账单编号、患者姓名或描述...\"\n            value={filters.search || ''}\n            onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}\n            className=\"pl-10\"\n          />\n        </div>\n        \n        {/* Advanced Filters Popover */}\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\n          <PopoverTrigger asChild>\n            <Button variant=\"outline\" className=\"relative\">\n              <IconFilter className=\"h-4 w-4 mr-2\" />\n              高级筛选\n              {activeFilterCount > 0 && (\n                <Badge \n                  variant=\"secondary\" \n                  className=\"ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\"\n                >\n                  {activeFilterCount}\n                </Badge>\n              )}\n            </Button>\n          </PopoverTrigger>\n          \n          <PopoverContent className=\"w-80 p-4\" align=\"end\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <h4 className=\"font-medium\">高级筛选</h4>\n                <Button variant=\"ghost\" size=\"sm\" onClick={() => setIsOpen(false)}>\n                  <IconX className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <Separator />\n\n              {/* Status Filter */}\n              <div className=\"space-y-2\">\n                <Label className=\"flex items-center gap-2\">\n                  <IconFileText className=\"h-4 w-4\" />\n                  账单状态\n                </Label>\n                <Select \n                  value={localFilters.status || ''} \n                  onValueChange={(value) => handleFilterChange('status', value || undefined)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"选择状态\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"\">全部状态</SelectItem>\n                    {billStatuses.map((status) => (\n                      <SelectItem key={status.value} value={status.value}>\n                        {status.label}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Bill Type Filter */}\n              <div className=\"space-y-2\">\n                <Label>账单类型</Label>\n                <Select \n                  value={localFilters.billType || ''} \n                  onValueChange={(value) => handleFilterChange('billType', value || undefined)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"选择类型\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"\">全部类型</SelectItem>\n                    {billTypes.map((type) => (\n                      <SelectItem key={type.value} value={type.value}>\n                        {type.label}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Patient Filter */}\n              {patients.length > 0 && (\n                <div className=\"space-y-2\">\n                  <Label className=\"flex items-center gap-2\">\n                    <IconUser className=\"h-4 w-4\" />\n                    患者\n                  </Label>\n                  <Select \n                    value={localFilters.patientId || ''} \n                    onValueChange={(value) => handleFilterChange('patientId', value || undefined)}\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"选择患者\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"\">全部患者</SelectItem>\n                      {patients.map((patient) => (\n                        <SelectItem key={patient.id} value={patient.id}>\n                          {patient.fullName} - {patient.phone}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n              )}\n\n              {/* Date Range Filter */}\n              <div className=\"space-y-2\">\n                <Label className=\"flex items-center gap-2\">\n                  <IconCalendar className=\"h-4 w-4\" />\n                  日期范围\n                </Label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\">开始日期</Label>\n                    <Input\n                      type=\"date\"\n                      value={localFilters.dateFrom || ''}\n                      onChange={(e) => handleFilterChange('dateFrom', e.target.value || undefined)}\n                      className=\"text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\">结束日期</Label>\n                    <Input\n                      type=\"date\"\n                      value={localFilters.dateTo || ''}\n                      onChange={(e) => handleFilterChange('dateTo', e.target.value || undefined)}\n                      className=\"text-sm\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Amount Range Filter */}\n              <div className=\"space-y-2\">\n                <Label className=\"flex items-center gap-2\">\n                  <IconCurrencyYuan className=\"h-4 w-4\" />\n                  金额范围\n                </Label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\">最小金额</Label>\n                    <Input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      placeholder=\"0.00\"\n                      value={localFilters.amountMin || ''}\n                      onChange={(e) => handleFilterChange('amountMin', parseFloat(e.target.value) || undefined)}\n                      className=\"text-sm\"\n                    />\n                  </div>\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\">最大金额</Label>\n                    <Input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      placeholder=\"无限制\"\n                      value={localFilters.amountMax || ''}\n                      onChange={(e) => handleFilterChange('amountMax', parseFloat(e.target.value) || undefined)}\n                      className=\"text-sm\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <Separator />\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-2\">\n                <Button onClick={applyFilters} className=\"flex-1\">\n                  应用筛选\n                </Button>\n                <Button variant=\"outline\" onClick={clearFilters}>\n                  清除\n                </Button>\n              </div>\n            </div>\n          </PopoverContent>\n        </Popover>\n      </div>\n\n      {/* Active Filters Display */}\n      {hasActiveFilters && (\n        <div className=\"flex flex-wrap gap-2\">\n          {filters.status && (\n            <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              状态: {billStatuses.find(s => s.value === filters.status)?.label}\n              <button\n                onClick={() => clearSingleFilter('status')}\n                className=\"ml-1 hover:bg-muted rounded-full p-0.5\"\n              >\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>\n          )}\n          \n          {filters.billType && (\n            <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              类型: {billTypes.find(t => t.value === filters.billType)?.label}\n              <button\n                onClick={() => clearSingleFilter('billType')}\n                className=\"ml-1 hover:bg-muted rounded-full p-0.5\"\n              >\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>\n          )}\n          \n          {filters.patientId && (\n            <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              患者: {patients.find(p => p.id === filters.patientId)?.fullName}\n              <button\n                onClick={() => clearSingleFilter('patientId')}\n                className=\"ml-1 hover:bg-muted rounded-full p-0.5\"\n              >\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>\n          )}\n          \n          {(filters.dateFrom || filters.dateTo) && (\n            <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              日期: {filters.dateFrom || '开始'} ~ {filters.dateTo || '结束'}\n              <button\n                onClick={() => {\n                  clearSingleFilter('dateFrom');\n                  clearSingleFilter('dateTo');\n                }}\n                className=\"ml-1 hover:bg-muted rounded-full p-0.5\"\n              >\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>\n          )}\n          \n          {(filters.amountMin !== undefined || filters.amountMax !== undefined) && (\n            <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              金额: {filters.amountMin ? billingUtils.formatCurrency(filters.amountMin) : '0'} ~ {filters.amountMax ? billingUtils.formatCurrency(filters.amountMax) : '∞'}\n              <button\n                onClick={() => {\n                  clearSingleFilter('amountMin');\n                  clearSingleFilter('amountMax');\n                }}\n                className=\"ml-1 hover:bg-muted rounded-full p-0.5\"\n              >\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>\n          )}\n          \n          {hasActiveFilters && (\n            <Button variant=\"ghost\" size=\"sm\" onClick={clearFilters} className=\"h-6 px-2\">\n              清除全部\n            </Button>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAnBA;;;;;;;;;;;;AAuCA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAS,OAAO;IAAK;IAC9B;QAAE,OAAO;QAAQ,OAAO;IAAM;IAC9B;QAAE,OAAO;QAAa,OAAO;IAAM;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAM;IAC9B;QAAE,OAAO;QAAa,OAAO;IAAM;CACpC;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAgB,OAAO;IAAO;IACvC;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAc,OAAO;IAAO;CACtC;AAEM,SAAS,YAAY,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,EAAE,SAAS,EAAoB;IAClG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAqB;IAEpE,MAAM,mBAAmB,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,QACnD,UAAU,aAAa,UAAU,MAAM,UAAU;IAGnD,MAAM,oBAAoB,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAA,QACtD,UAAU,aAAa,UAAU,MAAM,UAAU,MACjD,MAAM;IAER,MAAM,qBAAqB,CAAC,KAA8B;QACxD,MAAM,aAAa;YAAE,GAAG,YAAY;YAAE,CAAC,IAAI,EAAE;QAAM;QACnD,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,UAAU;IACZ;IAEA,MAAM,eAAe;QACnB,MAAM,eAAkC,CAAC;QACzC,gBAAgB;QAChB,gBAAgB;QAChB,UAAU;IACZ;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa;YAAE,GAAG,OAAO;QAAC;QAChC,OAAO,UAAU,CAAC,IAAI;QACtB,gBAAgB;IAClB;IAEA,qBACE,6VAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,wTAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6VAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,IAAM,gBAAgB;wCAAE,GAAG,OAAO;wCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACtE,WAAU;;;;;;;;;;;;kCAKd,6VAAC,mIAAA,CAAA,UAAO;wBAAC,MAAM;wBAAQ,cAAc;;0CACnC,6VAAC,mIAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6VAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,6VAAC,wTAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;wCAEtC,oBAAoB,mBACnB,6VAAC,iIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDAET;;;;;;;;;;;;;;;;;0CAMT,6VAAC,mIAAA,CAAA,iBAAc;gCAAC,WAAU;gCAAW,OAAM;0CACzC,cAAA,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAG,WAAU;8DAAc;;;;;;8DAC5B,6VAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS,IAAM,UAAU;8DACzD,cAAA,6VAAC,8SAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAIrB,6VAAC,qIAAA,CAAA,YAAS;;;;;sDAGV,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,6VAAC,4TAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGtC,6VAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,aAAa,MAAM,IAAI;oDAC9B,eAAe,CAAC,QAAU,mBAAmB,UAAU,SAAS;;sEAEhE,6VAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6VAAC,kIAAA,CAAA,gBAAa;;8EACZ,6VAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAG;;;;;;gEACpB,aAAa,GAAG,CAAC,CAAC,uBACjB,6VAAC,kIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAC/C,OAAO,KAAK;uEADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;sDASrC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6VAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,aAAa,QAAQ,IAAI;oDAChC,eAAe,CAAC,QAAU,mBAAmB,YAAY,SAAS;;sEAElE,6VAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6VAAC,kIAAA,CAAA,gBAAa;;8EACZ,6VAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAG;;;;;;gEACpB,UAAU,GAAG,CAAC,CAAC,qBACd,6VAAC,kIAAA,CAAA,aAAU;wEAAkB,OAAO,KAAK,KAAK;kFAC3C,KAAK,KAAK;uEADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;wCASlC,SAAS,MAAM,GAAG,mBACjB,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,6VAAC,oTAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGlC,6VAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,aAAa,SAAS,IAAI;oDACjC,eAAe,CAAC,QAAU,mBAAmB,aAAa,SAAS;;sEAEnE,6VAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,6VAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6VAAC,kIAAA,CAAA,gBAAa;;8EACZ,6VAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAG;;;;;;gEACpB,SAAS,GAAG,CAAC,CAAC,wBACb,6VAAC,kIAAA,CAAA,aAAU;wEAAkB,OAAO,QAAQ,EAAE;;4EAC3C,QAAQ,QAAQ;4EAAC;4EAAI,QAAQ,KAAK;;uEADpB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;sDAUrC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,6VAAC,4TAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGtC,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;;8EACC,6VAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,6VAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,OAAO,aAAa,QAAQ,IAAI;oEAChC,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK,IAAI;oEAClE,WAAU;;;;;;;;;;;;sEAGd,6VAAC;;8EACC,6VAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,6VAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,OAAO,aAAa,MAAM,IAAI;oEAC9B,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;oEAChE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAOlB,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,6VAAC,oUAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAG1C,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;;8EACC,6VAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,6VAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,KAAI;oEACJ,aAAY;oEACZ,OAAO,aAAa,SAAS,IAAI;oEACjC,UAAU,CAAC,IAAM,mBAAmB,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAC/E,WAAU;;;;;;;;;;;;sEAGd,6VAAC;;8EACC,6VAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,6VAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,KAAI;oEACJ,aAAY;oEACZ,OAAO,aAAa,SAAS,IAAI;oEACjC,UAAU,CAAC,IAAM,mBAAmB,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAC/E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMlB,6VAAC,qIAAA,CAAA,YAAS;;;;;sDAGV,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;oDAAc,WAAU;8DAAS;;;;;;8DAGlD,6VAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU1D,kCACC,6VAAC;gBAAI,WAAU;;oBACZ,QAAQ,MAAM,kBACb,6VAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAA0B;4BACxD,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,MAAM,GAAG;0CACzD,6VAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CAEV,cAAA,6VAAC,8SAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKtB,QAAQ,QAAQ,kBACf,6VAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAA0B;4BACxD,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,QAAQ,GAAG;0CACxD,6VAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CAEV,cAAA,6VAAC,8SAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKtB,QAAQ,SAAS,kBAChB,6VAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAA0B;4BACxD,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,SAAS,GAAG;0CACrD,6VAAC;gCACC,SAAS,IAAM,kBAAkB;gCACjC,WAAU;0CAEV,cAAA,6VAAC,8SAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKtB,CAAC,QAAQ,QAAQ,IAAI,QAAQ,MAAM,mBAClC,6VAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAA0B;4BACxD,QAAQ,QAAQ,IAAI;4BAAK;4BAAI,QAAQ,MAAM,IAAI;0CACpD,6VAAC;gCACC,SAAS;oCACP,kBAAkB;oCAClB,kBAAkB;gCACpB;gCACA,WAAU;0CAEV,cAAA,6VAAC,8SAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKtB,CAAC,QAAQ,SAAS,KAAK,aAAa,QAAQ,SAAS,KAAK,SAAS,mBAClE,6VAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAA0B;4BACxD,QAAQ,SAAS,GAAG,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,SAAS,IAAI;4BAAI;4BAAI,QAAQ,SAAS,GAAG,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,SAAS,IAAI;0CACvJ,6VAAC;gCACC,SAAS;oCACP,kBAAkB;oCAClB,kBAAkB;gCACpB;gCACA,WAAU;0CAEV,cAAA,6VAAC,8SAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAKtB,kCACC,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;wBAAc,WAAU;kCAAW;;;;;;;;;;;;;;;;;;AAQ1F", "debugId": null}}, {"offset": {"line": 7015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/billing-list.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport {\n  IconPlus,\n  IconReceipt,\n  IconSearch,\n  IconEdit,\n  IconEye,\n  IconCreditCard,\n  IconCash,\n  IconAlertCircle,\n  IconRefresh\n} from '@tabler/icons-react';\nimport { Bill, Payment } from '@/types/clinic';\nimport { toast } from 'sonner';\nimport { t } from '@/lib/translations';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { PaymentDialog } from './payment-dialog';\nimport { BillDialog } from './bill-dialog';\nimport { BillStatusManager } from './bill-status-manager';\nimport { BillFilters, BillFilterOptions } from './bill-filters';\n\n// Status mapping for bill status\nconst getStatusVariant = (status: string): \"default\" | \"secondary\" | \"destructive\" | \"outline\" => {\n  switch (status) {\n    case 'paid':\n      return 'default';\n    case 'confirmed':\n      return 'secondary';\n    case 'cancelled':\n      return 'destructive';\n    default:\n      return 'outline';\n  }\n};\n\nconst getStatusColor = (status: string): string => {\n  switch (status) {\n    case 'paid':\n      return 'text-green-600 bg-green-50 border-green-200';\n    case 'confirmed':\n      return 'text-blue-600 bg-blue-50 border-blue-200';\n    case 'cancelled':\n      return 'text-red-600 bg-red-50 border-red-200';\n    case 'draft':\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n    case 'sent':\n      return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n};\n\n// Status badge component\nconst StatusBadge = ({ status }: { status: string }) => {\n  const getStatusColor = (status: string) => {\n    switch (status.toLowerCase()) {\n      case 'paid':\n        return 'bg-green-100 text-green-800 hover:bg-green-200';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';\n      case 'partial':\n        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';\n      case 'overdue':\n        return 'bg-red-100 text-red-800 hover:bg-red-200';\n      case 'cancelled':\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n      default:\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status.toLowerCase()) {\n      case 'paid':\n        return '已支付';\n      case 'pending':\n        return '待支付';\n      case 'partial':\n        return '部分支付';\n      case 'overdue':\n        return '逾期';\n      case 'cancelled':\n        return '已取消';\n      default:\n        return status;\n    }\n  };\n\n  return (\n    <Badge className={getStatusColor(status)}>\n      {getStatusText(status)}\n    </Badge>\n  );\n};\n\n// Bill type badge component\nconst BillTypeBadge = ({ type }: { type: string }) => {\n  const getTypeColor = (type: string) => {\n    switch (type.toLowerCase()) {\n      case 'treatment':\n        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';\n      case 'consultation':\n        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';\n      case 'deposit':\n        return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200';\n      case 'additional':\n        return 'bg-pink-100 text-pink-800 hover:bg-pink-200';\n      default:\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n    }\n  };\n\n  const getTypeText = (type: string) => {\n    switch (type.toLowerCase()) {\n      case 'treatment':\n        return '治疗';\n      case 'consultation':\n        return '咨询';\n      case 'deposit':\n        return '押金';\n      case 'additional':\n        return '附加';\n      default:\n        return type;\n    }\n  };\n\n  return (\n    <Badge variant=\"outline\" className={getTypeColor(type)}>\n      {getTypeText(type)}\n    </Badge>\n  );\n};\n\nexport function BillingList() {\n  const { hasPermission } = useRole();\n  const [bills, setBills] = useState<Bill[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<BillFilterOptions>({});\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [refreshing, setRefreshing] = useState(false);\n  const [patients, setPatients] = useState<Array<{ id: string; fullName: string; phone: string }>>([]);\n  const [selectedBillForPayment, setSelectedBillForPayment] = useState<Bill | null>(null);\n  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);\n  const [selectedBillForEdit, setSelectedBillForEdit] = useState<Bill | null>(null);\n  const [isBillDialogOpen, setIsBillDialogOpen] = useState(false);\n  const [isCreatingBill, setIsCreatingBill] = useState(false);\n\n  // Fetch bills from API\n  const fetchBills = async (page: number = 1, currentFilters: BillFilterOptions = {}) => {\n    try {\n      setLoading(page === 1);\n      setError(null);\n\n      const response = await billsAPI.fetchBills({\n        page,\n        limit: 10,\n        search: currentFilters.search || undefined,\n        status: currentFilters.status || undefined,\n        patientId: currentFilters.patientId || undefined,\n        dateFrom: currentFilters.dateFrom || undefined,\n        dateTo: currentFilters.dateTo || undefined,\n      });\n\n      setBills(response.docs);\n      setCurrentPage(response.page);\n      setTotalPages(response.totalPages);\n    } catch (err) {\n      console.error('Failed to fetch bills:', err);\n      const errorMessage = err instanceof BillingAPIError\n        ? err.message\n        : '加载账单失败，请稍后重试。';\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // Fetch patients for filter dropdown\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch('/api/patients');\n      const data = await response.json();\n      setPatients(data.docs || []);\n    } catch (error) {\n      console.error('Failed to fetch patients:', error);\n    }\n  };\n\n  // Initial load\n  useEffect(() => {\n    fetchBills(1, filters);\n    fetchPatients();\n  }, []);\n\n  // Handle filter changes with debouncing\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      fetchBills(1, filters);\n    }, 500);\n\n    return () => clearTimeout(timeoutId);\n  }, [filters]);\n\n  // Refresh bills\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchBills(currentPage, filters);\n  };\n\n  const handleFiltersChange = (newFilters: BillFilterOptions) => {\n    setFilters(newFilters);\n    setCurrentPage(1); // Reset to first page when filters change\n  };\n\n  const handleNewBill = () => {\n    setSelectedBillForEdit(null);\n    setIsCreatingBill(true);\n    setIsBillDialogOpen(true);\n  };\n\n  const handleViewBill = (bill: Bill) => {\n    toast.info(`查看账单: ${bill.billNumber}`);\n    // TODO: Implement bill view dialog\n  };\n\n  const handleEditBill = (bill: Bill) => {\n    setSelectedBillForEdit(bill);\n    setIsCreatingBill(false);\n    setIsBillDialogOpen(true);\n  };\n\n  const handlePayment = (bill: Bill) => {\n    setSelectedBillForPayment(bill);\n    setIsPaymentDialogOpen(true);\n  };\n\n  const handlePaymentSuccess = (payment: Payment) => {\n    toast.success(`支付处理成功！收据编号: ${payment.receiptNumber || '待生成'}`);\n    // Refresh the bills list to show updated payment status\n    fetchBills(currentPage, filters);\n  };\n\n  const handleClosePaymentDialog = () => {\n    setIsPaymentDialogOpen(false);\n    setSelectedBillForPayment(null);\n  };\n\n  const handleBillSuccess = (bill: Bill) => {\n    const action = isCreatingBill ? '创建' : '更新';\n    toast.success(`账单${action}成功！账单编号: ${bill.billNumber}`);\n    // Refresh the bills list\n    fetchBills(currentPage, filters);\n  };\n\n  const handleCloseBillDialog = () => {\n    setIsBillDialogOpen(false);\n    setSelectedBillForEdit(null);\n    setIsCreatingBill(false);\n  };\n\n  const handleStatusUpdate = (updatedBill: Bill) => {\n    // Update the bill in the local state\n    setBills(prevBills =>\n      prevBills.map(bill =>\n        bill.id === updatedBill.id ? updatedBill : bill\n      )\n    );\n    toast.success('账单状态已更新');\n  };\n\n  const handlePageChange = (page: number) => {\n    fetchBills(page, filters);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载账单中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error && bills.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-red-600 mb-2\">加载失败</h3>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <Button onClick={handleRefresh} variant=\"outline\">\n            <IconRefresh className=\"h-4 w-4 mr-2\" />\n            重试\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  if (bills.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconReceipt className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium mb-2\">暂无账单</h3>\n          <p className=\"text-muted-foreground mb-4\">\n            开始创建您的第一个账单。\n          </p>\n          <PermissionGate permission=\"canCreateBills\">\n            <Button onClick={handleNewBill}>\n              <IconPlus className=\"h-4 w-4 mr-2\" />\n              新建账单\n            </Button>\n          </PermissionGate>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Advanced Filters */}\n      <BillFilters\n        filters={filters}\n        onFiltersChange={handleFiltersChange}\n        patients={patients}\n      />\n\n      {/* Header with actions */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleRefresh}\n            disabled={refreshing}\n          >\n            <IconRefresh className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n            刷新\n          </Button>\n          <div className=\"text-sm text-muted-foreground\">\n            共 {bills.length} 个账单\n          </div>\n        </div>\n        <PermissionGate permission=\"canCreateBills\">\n          <Button onClick={handleNewBill}>\n            <IconPlus className=\"h-4 w-4 mr-2\" />\n            新建账单\n          </Button>\n        </PermissionGate>\n      </div>\n\n      {/* Error banner for non-fatal errors */}\n      {error && bills.length > 0 && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <div className=\"flex items-center\">\n            <IconAlertCircle className=\"h-4 w-4 text-red-500 mr-2\" />\n            <span className=\"text-red-700 text-sm\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Bills Grid */}\n      <div className=\"grid gap-4\">\n        {bills.map((bill) => (\n          <div key={bill.id} className=\"border rounded-lg p-4 space-y-3\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center gap-2\">\n                  <h4 className=\"font-medium text-lg\">{bill.billNumber}</h4>\n                  <BillTypeBadge type={bill.billType} />\n                  <BillStatusManager\n                    bill={bill}\n                    onStatusUpdate={handleStatusUpdate}\n                    trigger={<StatusBadge status={bill.status} />}\n                  />\n                </div>\n                <div className=\"text-sm text-muted-foreground\">\n                  <p>患者: {typeof bill.patient === 'object' ? bill.patient.fullName : '未知患者'}</p>\n                  <p>描述: {bill.description}</p>\n                  <p>开票日期: {new Date(bill.issueDate).toLocaleDateString('zh-CN')}</p>\n                </div>\n              </div>\n              \n              <div className=\"text-right space-y-1\">\n                <div className=\"text-lg font-semibold\">\n                  {billingUtils.formatCurrency(bill.totalAmount)}\n                </div>\n                {(bill.remainingAmount || 0) > 0 && (\n                  <div className=\"text-sm text-red-600\">\n                    待收: {billingUtils.formatCurrency(bill.remainingAmount || 0)}\n                  </div>\n                )}\n                {(bill.paidAmount || 0) > 0 && (\n                  <div className=\"text-sm text-green-600\">\n                    已收: {billingUtils.formatCurrency(bill.paidAmount || 0)}\n                  </div>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-between pt-2 border-t\">\n              <div className=\"text-xs text-muted-foreground\">\n                到期日期: {new Date(bill.dueDate).toLocaleDateString('zh-CN')}\n              </div>\n              \n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" onClick={() => handleViewBill(bill)}>\n                  <IconEye className=\"h-4 w-4\" />\n                </Button>\n                <PermissionGate permission=\"canEditBills\">\n                  <Button variant=\"ghost\" size=\"sm\" onClick={() => handleEditBill(bill)}>\n                    <IconEdit className=\"h-4 w-4\" />\n                  </Button>\n                </PermissionGate>\n                {(bill.remainingAmount || 0) > 0 && (\n                  <PermissionGate permission=\"canProcessPayments\">\n                    <Button variant=\"default\" size=\"sm\" onClick={() => handlePayment(bill)}>\n                      <IconCreditCard className=\"h-4 w-4 mr-1\" />\n                      收款\n                    </Button>\n                  </PermissionGate>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <div className=\"flex items-center justify-center space-x-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handlePageChange(currentPage - 1)}\n            disabled={currentPage <= 1}\n          >\n            上一页\n          </Button>\n          <span className=\"text-sm text-muted-foreground\">\n            第 {currentPage} 页，共 {totalPages} 页\n          </span>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => handlePageChange(currentPage + 1)}\n            disabled={currentPage >= totalPages}\n          >\n            下一页\n          </Button>\n        </div>\n      )}\n\n      {/* Payment Dialog */}\n      <PaymentDialog\n        bill={selectedBillForPayment}\n        isOpen={isPaymentDialogOpen}\n        onClose={handleClosePaymentDialog}\n        onSuccess={handlePaymentSuccess}\n      />\n\n      {/* Bill Creation/Edit Dialog */}\n      <BillDialog\n        bill={selectedBillForEdit}\n        isOpen={isBillDialogOpen}\n        onClose={handleCloseBillDialog}\n        onSuccess={handleBillSuccess}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAEA;AACA;AACA;AACA;AACA;AAzBA;;;;;;;;;;;;;AA2BA,iCAAiC;AACjC,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,yBAAyB;AACzB,MAAM,cAAc,CAAC,EAAE,MAAM,EAAsB;IACjD,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,WAAW,eAAe;kBAC9B,cAAc;;;;;;AAGrB;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAoB;IAC/C,MAAM,eAAe,CAAC;QACpB,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6VAAC,iIAAA,CAAA,QAAK;QAAC,SAAQ;QAAU,WAAW,aAAa;kBAC9C,YAAY;;;;;;AAGnB;AAEO,SAAS;IACd,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA0D,EAAE;IACnG,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAe;IAClF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAe;IAC5E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,uBAAuB;IACvB,MAAM,aAAa,OAAO,OAAe,CAAC,EAAE,iBAAoC,CAAC,CAAC;QAChF,IAAI;YACF,WAAW,SAAS;YACpB,SAAS;YAET,MAAM,WAAW,MAAM,4HAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;gBACzC;gBACA,OAAO;gBACP,QAAQ,eAAe,MAAM,IAAI;gBACjC,QAAQ,eAAe,MAAM,IAAI;gBACjC,WAAW,eAAe,SAAS,IAAI;gBACvC,UAAU,eAAe,QAAQ,IAAI;gBACrC,QAAQ,eAAe,MAAM,IAAI;YACnC;YAEA,SAAS,SAAS,IAAI;YACtB,eAAe,SAAS,IAAI;YAC5B,cAAc,SAAS,UAAU;QACnC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAAe,eAAe,4HAAA,CAAA,kBAAe,GAC/C,IAAI,OAAO,GACX;YACJ,SAAS;YACT,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;YACX,cAAc;QAChB;IACF;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,IAAI,IAAI,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,eAAe;IACf,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW,GAAG;QACd;IACF,GAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,WAAW,GAAG;QAChB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAQ;IAEZ,gBAAgB;IAChB,MAAM,gBAAgB;QACpB,cAAc;QACd,MAAM,WAAW,aAAa;IAChC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,eAAe,IAAI,0CAA0C;IAC/D;IAEA,MAAM,gBAAgB;QACpB,uBAAuB;QACvB,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,UAAU,EAAE;IACrC,mCAAmC;IACrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,uBAAuB;QACvB,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,gBAAgB,CAAC;QACrB,0BAA0B;QAC1B,uBAAuB;IACzB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,QAAQ,aAAa,IAAI,OAAO;QAC9D,wDAAwD;QACxD,WAAW,aAAa;IAC1B;IAEA,MAAM,2BAA2B;QAC/B,uBAAuB;QACvB,0BAA0B;IAC5B;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,SAAS,iBAAiB,OAAO;QACvC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,SAAS,EAAE,KAAK,UAAU,EAAE;QACtD,yBAAyB;QACzB,WAAW,aAAa;IAC1B;IAEA,MAAM,wBAAwB;QAC5B,oBAAoB;QACpB,uBAAuB;QACvB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,qCAAqC;QACrC,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;QAG/C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW,MAAM;IACnB;IAEA,IAAI,SAAS;QACX,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,SAAS,MAAM,MAAM,KAAK,GAAG;QAC/B,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,kUAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,6VAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6VAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAC3C,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAe,SAAQ;;0CACtC,6VAAC,0TAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAMlD;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,0TAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6VAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,6VAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6VAAC,8HAAA,CAAA,iBAAc;wBAAC,YAAW;kCACzB,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6VAAC,oTAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOjD;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC,gJAAA,CAAA,cAAW;gBACV,SAAS;gBACT,iBAAiB;gBACjB,UAAU;;;;;;0BAIZ,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;;kDAEV,6VAAC,0TAAA,CAAA,cAAW;wCAAC,WAAW,CAAC,aAAa,EAAE,aAAa,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;0CAGhF,6VAAC;gCAAI,WAAU;;oCAAgC;oCAC1C,MAAM,MAAM;oCAAC;;;;;;;;;;;;;kCAGpB,6VAAC,8HAAA,CAAA,iBAAc;wBAAC,YAAW;kCACzB,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,6VAAC,oTAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAO1C,SAAS,MAAM,MAAM,GAAG,mBACvB,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,kUAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;sCAC3B,6VAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAM9C,6VAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6VAAC;wBAAkB,WAAU;;0CAC3B,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAG,WAAU;kEAAuB,KAAK,UAAU;;;;;;kEACpD,6VAAC;wDAAc,MAAM,KAAK,QAAQ;;;;;;kEAClC,6VAAC,0JAAA,CAAA,oBAAiB;wDAChB,MAAM;wDACN,gBAAgB;wDAChB,uBAAS,6VAAC;4DAAY,QAAQ,KAAK,MAAM;;;;;;;;;;;;;;;;;0DAG7C,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;;4DAAE;4DAAK,OAAO,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,CAAC,QAAQ,GAAG;;;;;;;kEACnE,6VAAC;;4DAAE;4DAAK,KAAK,WAAW;;;;;;;kEACxB,6VAAC;;4DAAE;4DAAO,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;kDAI1D,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;0DACZ,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,WAAW;;;;;;4CAE9C,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,mBAC7B,6VAAC;gDAAI,WAAU;;oDAAuB;oDAC/B,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,eAAe,IAAI;;;;;;;4CAG5D,CAAC,KAAK,UAAU,IAAI,CAAC,IAAI,mBACxB,6VAAC;gDAAI,WAAU;;oDAAyB;oDACjC,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,UAAU,IAAI;;;;;;;;;;;;;;;;;;;0CAM5D,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;4CAAgC;4CACtC,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB,CAAC;;;;;;;kDAGnD,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS,IAAM,eAAe;0DAC9D,cAAA,6VAAC,kTAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6VAAC,8HAAA,CAAA,iBAAc;gDAAC,YAAW;0DACzB,cAAA,6VAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS,IAAM,eAAe;8DAC9D,cAAA,6VAAC,oTAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;;;;;4CAGvB,CAAC,KAAK,eAAe,IAAI,CAAC,IAAI,mBAC7B,6VAAC,8HAAA,CAAA,iBAAc;gDAAC,YAAW;0DACzB,cAAA,6VAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;oDAAK,SAAS,IAAM,cAAc;;sEAC/D,6VAAC,gUAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;uBArD7C,KAAK,EAAE;;;;;;;;;;YAiEpB,aAAa,mBACZ,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,eAAe;kCAC1B;;;;;;kCAGD,6VAAC;wBAAK,WAAU;;4BAAgC;4BAC3C;4BAAY;4BAAM;4BAAW;;;;;;;kCAElC,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,eAAe;kCAC1B;;;;;;;;;;;;0BAOL,6VAAC,kJAAA,CAAA,gBAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,WAAW;;;;;;0BAIb,6VAAC,+IAAA,CAAA,aAAU;gBACT,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 7879, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/appointment-to-bill.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { \n  IconReceipt, \n  IconCalendar, \n  IconUser,\n  IconStethoscope,\n  IconCurrencyYuan,\n  IconCheck,\n  IconAlertTriangle,\n  IconRefresh\n} from '@tabler/icons-react';\nimport { Appointment, Bill } from '@/types/clinic';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { billingNotifications } from '@/lib/billing-notifications';\nimport { toast } from 'sonner';\n\ninterface AppointmentToBillProps {\n  onBillGenerated?: (bill: Bill) => void;\n  className?: string;\n}\n\ninterface AppointmentWithBillStatus extends Appointment {\n  hasBill?: boolean;\n  billId?: string;\n  billNumber?: string;\n}\n\nexport function AppointmentToBill({ onBillGenerated, className }: AppointmentToBillProps) {\n  const { hasPermission } = useRole();\n  const [appointments, setAppointments] = useState<AppointmentWithBillStatus[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [generating, setGenerating] = useState<string | null>(null);\n  const [selectedBillType, setSelectedBillType] = useState<string>('treatment');\n\n  // Check permissions\n  if (!hasPermission('canCreateBills')) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertTriangle className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">权限不足</h3>\n          <p className=\"text-muted-foreground\">\n            您没有权限从预约生成账单\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  const fetchCompletedAppointments = async () => {\n    try {\n      setLoading(true);\n      \n      // Fetch completed appointments\n      const appointmentsResponse = await fetch('/api/appointments?status=completed&limit=50');\n      const appointmentsData = await appointmentsResponse.json();\n      \n      if (!appointmentsResponse.ok) {\n        throw new Error(appointmentsData.error || 'Failed to fetch appointments');\n      }\n\n      // Fetch existing bills to check which appointments already have bills\n      const billsResponse = await billsAPI.fetchBills({ limit: 100 });\n      const existingBills = billsResponse.docs;\n\n      // Map appointments with bill status\n      const appointmentsWithBillStatus: AppointmentWithBillStatus[] = appointmentsData.docs.map((appointment: Appointment) => {\n        const existingBill = existingBills.find(bill => \n          typeof bill.appointment === 'object' && bill.appointment?.id === appointment.id\n        );\n        \n        return {\n          ...appointment,\n          hasBill: !!existingBill,\n          billId: existingBill?.id,\n          billNumber: existingBill?.billNumber,\n        };\n      });\n\n      setAppointments(appointmentsWithBillStatus);\n    } catch (error) {\n      console.error('Failed to fetch appointments:', error);\n      billingNotifications.system.dataRefreshError();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchCompletedAppointments();\n  }, []);\n\n  const handleGenerateBill = async (appointment: AppointmentWithBillStatus) => {\n    try {\n      setGenerating(appointment.id);\n\n      const response = await billsAPI.generateFromAppointment(appointment.id, selectedBillType);\n      \n      billingNotifications.bill.generateFromAppointment(\n        response,\n        new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')\n      );\n\n      // Update the appointment status locally\n      setAppointments(prev => \n        prev.map(apt => \n          apt.id === appointment.id \n            ? { ...apt, hasBill: true, billId: response.id, billNumber: response.billNumber }\n            : apt\n        )\n      );\n\n      if (onBillGenerated) {\n        onBillGenerated(response);\n      }\n\n    } catch (error) {\n      console.error('Failed to generate bill:', error);\n      const errorMessage = error instanceof BillingAPIError \n        ? error.message \n        : undefined;\n      billingNotifications.bill.createError(errorMessage);\n    } finally {\n      setGenerating(null);\n    }\n  };\n\n  const getAppointmentStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'confirmed':\n        return 'bg-blue-100 text-blue-800';\n      case 'scheduled':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getAppointmentStatusLabel = (status: string) => {\n    const labels = {\n      scheduled: '已预约',\n      confirmed: '已确认',\n      completed: '已完成',\n      cancelled: '已取消',\n      'no-show': '未到诊',\n    };\n    return labels[status as keyof typeof labels] || status;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载已完成预约中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const unbilledAppointments = appointments.filter(apt => !apt.hasBill);\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold tracking-tight flex items-center gap-2\">\n            <IconReceipt className=\"size-6\" />\n            预约生成账单\n          </h2>\n          <p className=\"text-muted-foreground\">\n            从已完成的预约自动生成账单\n          </p>\n        </div>\n        <Button variant=\"outline\" onClick={fetchCompletedAppointments} disabled={loading}>\n          <IconRefresh className=\"h-4 w-4 mr-2\" />\n          刷新\n        </Button>\n      </div>\n\n      {/* Bill Type Selection */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">账单类型设置</CardTitle>\n          <CardDescription>\n            选择生成账单的默认类型\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Select value={selectedBillType} onValueChange={setSelectedBillType}>\n            <SelectTrigger className=\"w-64\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"treatment\">治疗账单</SelectItem>\n              <SelectItem value=\"consultation\">咨询账单</SelectItem>\n              <SelectItem value=\"additional\">补充账单</SelectItem>\n            </SelectContent>\n          </Select>\n        </CardContent>\n      </Card>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-blue-600\">\n                {appointments.length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">已完成预约</div>\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-green-600\">\n                {appointments.filter(apt => apt.hasBill).length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">已生成账单</div>\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-orange-600\">\n                {unbilledAppointments.length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">待生成账单</div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Appointments List */}\n      {unbilledAppointments.length === 0 ? (\n        <Card>\n          <CardContent className=\"py-8\">\n            <div className=\"text-center\">\n              <IconCheck className=\"h-12 w-12 text-green-500 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium mb-2\">全部预约已生成账单</h3>\n              <p className=\"text-muted-foreground\">\n                所有已完成的预约都已生成对应的账单\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      ) : (\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">待生成账单的预约</h3>\n          \n          <div className=\"grid gap-4\">\n            {unbilledAppointments.map((appointment) => (\n              <Card key={appointment.id} className=\"hover:shadow-md transition-shadow\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"space-y-3 flex-1\">\n                      {/* Appointment Header */}\n                      <div className=\"flex items-center gap-3\">\n                        <Badge className={getAppointmentStatusColor(appointment.status)}>\n                          {getAppointmentStatusLabel(appointment.status)}\n                        </Badge>\n                        <span className=\"text-sm text-muted-foreground\">\n                          预约ID: {appointment.id}\n                        </span>\n                      </div>\n\n                      {/* Appointment Details */}\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm\">\n                        <div className=\"flex items-center gap-2\">\n                          <IconCalendar className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')}\n                          </span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <IconUser className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {typeof appointment.patient === 'object' \n                              ? appointment.patient.fullName \n                              : '未知患者'}\n                          </span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <IconStethoscope className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {typeof appointment.treatment === 'object' \n                              ? appointment.treatment.name \n                              : '未知治疗'}\n                          </span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <IconCurrencyYuan className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {billingUtils.formatCurrency(appointment.price || 0)}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Generate Bill Button */}\n                    <div className=\"ml-4\">\n                      <Button\n                        onClick={() => handleGenerateBill(appointment)}\n                        disabled={generating === appointment.id}\n                        className=\"min-w-24\"\n                      >\n                        {generating === appointment.id ? (\n                          <>\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                            生成中...\n                          </>\n                        ) : (\n                          <>\n                            <IconReceipt className=\"h-4 w-4 mr-2\" />\n                            生成账单\n                          </>\n                        )}\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Info Alert */}\n      <Alert>\n        <IconAlertTriangle className=\"h-4 w-4\" />\n        <AlertDescription>\n          生成的账单将包含预约的治疗项目、价格和患者信息。您可以在账单列表中进一步编辑账单详情。\n        </AlertDescription>\n      </Alert>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAtBA;;;;;;;;;;;;AAoCO,SAAS,kBAAkB,EAAE,eAAe,EAAE,SAAS,EAA0B;IACtF,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAA+B,EAAE;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,oBAAoB;IACpB,IAAI,CAAC,cAAc,mBAAmB;QACpC,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,sUAAA,CAAA,oBAAiB;wBAAC,WAAU;;;;;;kCAC7B,6VAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,MAAM,6BAA6B;QACjC,IAAI;YACF,WAAW;YAEX,+BAA+B;YAC/B,MAAM,uBAAuB,MAAM,MAAM;YACzC,MAAM,mBAAmB,MAAM,qBAAqB,IAAI;YAExD,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBAC5B,MAAM,IAAI,MAAM,iBAAiB,KAAK,IAAI;YAC5C;YAEA,sEAAsE;YACtE,MAAM,gBAAgB,MAAM,4HAAA,CAAA,WAAQ,CAAC,UAAU,CAAC;gBAAE,OAAO;YAAI;YAC7D,MAAM,gBAAgB,cAAc,IAAI;YAExC,oCAAoC;YACpC,MAAM,6BAA0D,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzF,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,OACtC,OAAO,KAAK,WAAW,KAAK,YAAY,KAAK,WAAW,EAAE,OAAO,YAAY,EAAE;gBAGjF,OAAO;oBACL,GAAG,WAAW;oBACd,SAAS,CAAC,CAAC;oBACX,QAAQ,cAAc;oBACtB,YAAY,cAAc;gBAC5B;YACF;YAEA,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,sIAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,gBAAgB;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,cAAc,YAAY,EAAE;YAE5B,MAAM,WAAW,MAAM,4HAAA,CAAA,WAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,EAAE;YAExE,sIAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,uBAAuB,CAC/C,UACA,IAAI,KAAK,YAAY,eAAe,EAAE,kBAAkB,CAAC;YAG3D,wCAAwC;YACxC,gBAAgB,CAAA,OACd,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,YAAY,EAAE,GACrB;wBAAE,GAAG,GAAG;wBAAE,SAAS;wBAAM,QAAQ,SAAS,EAAE;wBAAE,YAAY,SAAS,UAAU;oBAAC,IAC9E;YAIR,IAAI,iBAAiB;gBACnB,gBAAgB;YAClB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,iBAAiB,4HAAA,CAAA,kBAAe,GACjD,MAAM,OAAO,GACb;YACJ,sIAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,SAAS;YACb,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,IAAI,SAAS;QACX,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,OAAO;IAEpE,qBACE,6VAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;;kDACZ,6VAAC,0TAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAW;;;;;;;0CAGpC,6VAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAA4B,UAAU;;0CACvE,6VAAC,0TAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAM5C,6VAAC,gIAAA,CAAA,OAAI;;kCACH,6VAAC,gIAAA,CAAA,aAAU;;0CACT,6VAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,6VAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6VAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAkB,eAAe;;8CAC9C,6VAAC,kIAAA,CAAA,gBAAa;oCAAC,WAAU;8CACvB,cAAA,6VAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8CAEd,6VAAC,kIAAA,CAAA,gBAAa;;sDACZ,6VAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAY;;;;;;sDAC9B,6VAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAe;;;;;;sDACjC,6VAAC,kIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;kDACZ,aAAa,MAAM;;;;;;kDAEtB,6VAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;kCAKrD,6VAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;kDACZ,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO,EAAE,MAAM;;;;;;kDAEjD,6VAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;kCAKrD,6VAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;kDACZ,qBAAqB,MAAM;;;;;;kDAE9B,6VAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOtD,qBAAqB,MAAM,KAAK,kBAC/B,6VAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,sTAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6VAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,6VAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;qCAO3C,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAG,WAAU;kCAAwB;;;;;;kCAEtC,6VAAC;wBAAI,WAAU;kCACZ,qBAAqB,GAAG,CAAC,CAAC,4BACzB,6VAAC,gIAAA,CAAA,OAAI;gCAAsB,WAAU;0CACnC,cAAA,6VAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEAEb,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,iIAAA,CAAA,QAAK;gEAAC,WAAW,0BAA0B,YAAY,MAAM;0EAC3D,0BAA0B,YAAY,MAAM;;;;;;0EAE/C,6VAAC;gEAAK,WAAU;;oEAAgC;oEACvC,YAAY,EAAE;;;;;;;;;;;;;kEAKzB,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,4TAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;kFACxB,6VAAC;kFACE,IAAI,KAAK,YAAY,eAAe,EAAE,kBAAkB,CAAC;;;;;;;;;;;;0EAI9D,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,oTAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6VAAC;kFACE,OAAO,YAAY,OAAO,KAAK,WAC5B,YAAY,OAAO,CAAC,QAAQ,GAC5B;;;;;;;;;;;;0EAIR,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,kUAAA,CAAA,kBAAe;wEAAC,WAAU;;;;;;kFAC3B,6VAAC;kFACE,OAAO,YAAY,SAAS,KAAK,WAC9B,YAAY,SAAS,CAAC,IAAI,GAC1B;;;;;;;;;;;;0EAIR,6VAAC;gEAAI,WAAU;;kFACb,6VAAC,oUAAA,CAAA,mBAAgB;wEAAC,WAAU;;;;;;kFAC5B,6VAAC;kFACE,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,YAAY,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0DAO1D,6VAAC;gDAAI,WAAU;0DACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,mBAAmB;oDAClC,UAAU,eAAe,YAAY,EAAE;oDACvC,WAAU;8DAET,eAAe,YAAY,EAAE,iBAC5B;;0EACE,6VAAC;gEAAI,WAAU;;;;;;4DAAuE;;qFAIxF;;0EACE,6VAAC,0TAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;+BAhE3C,YAAY,EAAE;;;;;;;;;;;;;;;;0BA+EjC,6VAAC,iIAAA,CAAA,QAAK;;kCACJ,6VAAC,sUAAA,CAAA,oBAAiB;wBAAC,WAAU;;;;;;kCAC7B,6VAAC,iIAAA,CAAA,mBAAgB;kCAAC;;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 8657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/receipt-manager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  IconSearch, \n  IconReceipt, \n  IconPrinter, \n  IconDownload,\n  IconEye,\n  IconRefresh,\n  IconAlertCircle\n} from '@tabler/icons-react';\nimport { Payment, Bill } from '@/types/clinic';\nimport { paymentsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { ReceiptDialog } from './receipt-dialog';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { toast } from 'sonner';\n\ninterface ReceiptManagerProps {\n  className?: string;\n}\n\nexport function ReceiptManager({ className }: ReceiptManagerProps) {\n  const { hasPermission } = useRole();\n  const [payments, setPayments] = useState<Payment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);\n  const [showReceiptDialog, setShowReceiptDialog] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // Fetch payments with receipts\n  const fetchPayments = async (search?: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const response = await paymentsAPI.fetchPayments({\n        limit: 50,\n        status: 'completed', // Only show completed payments\n        search: search || undefined,\n      });\n      \n      // Filter payments that have receipt numbers\n      const paymentsWithReceipts = response.docs.filter(payment => payment.receiptNumber);\n      setPayments(paymentsWithReceipts);\n    } catch (err) {\n      console.error('Failed to fetch payments:', err);\n      const errorMessage = err instanceof BillingAPIError \n        ? err.message \n        : '加载收据失败，请稍后重试。';\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // Initial load\n  useEffect(() => {\n    fetchPayments();\n  }, []);\n\n  // Handle search with debouncing\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      if (searchTerm !== undefined) {\n        fetchPayments(searchTerm);\n      }\n    }, 500);\n\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchPayments(searchTerm);\n  };\n\n  const handleViewReceipt = (payment: Payment) => {\n    setSelectedPayment(payment);\n    setShowReceiptDialog(true);\n  };\n\n  const handlePrintReceipt = (payment: Payment) => {\n    // This would trigger the print functionality\n    setSelectedPayment(payment);\n    setShowReceiptDialog(true);\n    // The receipt dialog will handle the printing\n  };\n\n  const filteredPayments = payments.filter(payment =>\n    payment.receiptNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    payment.paymentNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (typeof payment.patient === 'object' && \n     payment.patient.fullName.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载收据中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error && payments.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-red-600 mb-2\">加载失败</h3>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <Button onClick={handleRefresh} variant=\"outline\">\n            <IconRefresh className=\"h-4 w-4 mr-2\" />\n            重试\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold tracking-tight flex items-center gap-2\">\n            <IconReceipt className=\"size-6\" />\n            收据管理\n          </h2>\n          <p className=\"text-muted-foreground\">\n            查看、打印和管理支付收据\n          </p>\n        </div>\n      </div>\n\n      {/* Search and Actions */}\n      <div className=\"flex items-center justify-between gap-4\">\n        <div className=\"flex items-center gap-2 flex-1 max-w-md\">\n          <div className=\"relative flex-1\">\n            <IconSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n            <Input\n              placeholder=\"搜索收据编号、支付编号或患者姓名...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          <Button \n            variant=\"outline\" \n            size=\"sm\" \n            onClick={handleRefresh}\n            disabled={refreshing}\n          >\n            <IconRefresh className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />\n          </Button>\n        </div>\n        \n        <div className=\"text-sm text-muted-foreground\">\n          共 {filteredPayments.length} 张收据\n        </div>\n      </div>\n\n      {/* Error banner for non-fatal errors */}\n      {error && payments.length > 0 && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <div className=\"flex items-center\">\n            <IconAlertCircle className=\"h-4 w-4 text-red-500 mr-2\" />\n            <span className=\"text-red-700 text-sm\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Receipts List */}\n      {filteredPayments.length === 0 ? (\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <IconReceipt className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium mb-2\">暂无收据</h3>\n            <p className=\"text-muted-foreground\">\n              {searchTerm ? '没有找到匹配的收据' : '还没有生成任何收据'}\n            </p>\n          </div>\n        </div>\n      ) : (\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {filteredPayments.map((payment) => (\n            <Card key={payment.id} className=\"hover:shadow-md transition-shadow\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-start justify-between\">\n                  <div>\n                    <CardTitle className=\"text-sm font-medium\">\n                      {payment.receiptNumber}\n                    </CardTitle>\n                    <CardDescription className=\"text-xs\">\n                      支付编号: {payment.paymentNumber}\n                    </CardDescription>\n                  </div>\n                  <Badge variant={payment.paymentStatus === 'completed' ? 'default' : 'secondary'}>\n                    {billingUtils.getPaymentStatusName(payment.paymentStatus)}\n                  </Badge>\n                </div>\n              </CardHeader>\n\n              <CardContent className=\"space-y-3\">\n                <div className=\"space-y-1 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">患者:</span>\n                    <span>\n                      {typeof payment.patient === 'object' \n                        ? payment.patient.fullName \n                        : '未知患者'}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">支付方式:</span>\n                    <span>{billingUtils.getPaymentMethodName(payment.paymentMethod)}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">支付日期:</span>\n                    <span>{new Date(payment.paymentDate).toLocaleDateString('zh-CN')}</span>\n                  </div>\n                  <div className=\"flex justify-between font-medium\">\n                    <span className=\"text-muted-foreground\">金额:</span>\n                    <span className=\"text-green-600\">\n                      {billingUtils.formatCurrency(payment.amount)}\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center gap-2 pt-2 border-t\">\n                  <Button \n                    variant=\"outline\" \n                    size=\"sm\" \n                    onClick={() => handleViewReceipt(payment)}\n                    className=\"flex-1\"\n                  >\n                    <IconEye className=\"h-4 w-4 mr-1\" />\n                    查看\n                  </Button>\n                  <PermissionGate permission=\"canGenerateReceipts\">\n                    <Button \n                      variant=\"outline\" \n                      size=\"sm\" \n                      onClick={() => handlePrintReceipt(payment)}\n                    >\n                      <IconPrinter className=\"h-4 w-4\" />\n                    </Button>\n                  </PermissionGate>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {/* Receipt Dialog */}\n      <ReceiptDialog\n        payment={selectedPayment}\n        isOpen={showReceiptDialog}\n        onClose={() => {\n          setShowReceiptDialog(false);\n          setSelectedPayment(null);\n        }}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;AA0BO,SAAS,eAAe,EAAE,SAAS,EAAuB;IAC/D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,+BAA+B;IAC/B,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,4HAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAC/C,OAAO;gBACP,QAAQ;gBACR,QAAQ,UAAU;YACpB;YAEA,4CAA4C;YAC5C,MAAM,uBAAuB,SAAS,IAAI,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,aAAa;YAClF,YAAY;QACd,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,eAAe,eAAe,4HAAA,CAAA,kBAAe,GAC/C,IAAI,OAAO,GACX;YACJ,SAAS;YACT,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;YACX,cAAc;QAChB;IACF;IAEA,eAAe;IACf,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,eAAe,WAAW;gBAC5B,cAAc;YAChB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAW;IAEf,MAAM,gBAAgB;QACpB,cAAc;QACd,MAAM,cAAc;IACtB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,6CAA6C;QAC7C,mBAAmB;QACnB,qBAAqB;IACrB,8CAA8C;IAChD;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,QAAQ,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OACpE,QAAQ,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGzE,IAAI,SAAS;QACX,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,SAAS,SAAS,MAAM,KAAK,GAAG;QAClC,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,kUAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,6VAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6VAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAC3C,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAe,SAAQ;;0CACtC,6VAAC,0TAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAMlD;IAEA,qBACE,6VAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;;sCACC,6VAAC;4BAAG,WAAU;;8CACZ,6VAAC,0TAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAW;;;;;;;sCAGpC,6VAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAOzC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,wTAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6VAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAGd,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;0CAEV,cAAA,6VAAC,0TAAA,CAAA,cAAW;oCAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,iBAAiB,IAAI;;;;;;;;;;;;;;;;;kCAIzE,6VAAC;wBAAI,WAAU;;4BAAgC;4BAC1C,iBAAiB,MAAM;4BAAC;;;;;;;;;;;;;YAK9B,SAAS,SAAS,MAAM,GAAG,mBAC1B,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,kUAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;sCAC3B,6VAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAM7C,iBAAiB,MAAM,KAAK,kBAC3B,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,0TAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6VAAC;4BAAG,WAAU;sCAA2B;;;;;;sCACzC,6VAAC;4BAAE,WAAU;sCACV,aAAa,cAAc;;;;;;;;;;;;;;;;qCAKlC,6VAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6VAAC,gIAAA,CAAA,OAAI;wBAAkB,WAAU;;0CAC/B,6VAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;;8DACC,6VAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,QAAQ,aAAa;;;;;;8DAExB,6VAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;;wDAAU;wDAC5B,QAAQ,aAAa;;;;;;;;;;;;;sDAGhC,6VAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,QAAQ,aAAa,KAAK,cAAc,YAAY;sDACjE,4HAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,QAAQ,aAAa;;;;;;;;;;;;;;;;;0CAK9D,6VAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6VAAC;kEACE,OAAO,QAAQ,OAAO,KAAK,WACxB,QAAQ,OAAO,CAAC,QAAQ,GACxB;;;;;;;;;;;;0DAGR,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6VAAC;kEAAM,4HAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,QAAQ,aAAa;;;;;;;;;;;;0DAEhE,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6VAAC;kEAAM,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC;;;;;;;;;;;;0DAE1D,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6VAAC;wDAAK,WAAU;kEACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;kDAKjD,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,kBAAkB;gDACjC,WAAU;;kEAEV,6VAAC,kTAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGtC,6VAAC,8HAAA,CAAA,iBAAc;gDAAC,YAAW;0DACzB,cAAA,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,mBAAmB;8DAElC,cAAA,6VAAC,0TAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA3DtB,QAAQ,EAAE;;;;;;;;;;0BAsE3B,6VAAC,kJAAA,CAAA,gBAAa;gBACZ,SAAS;gBACT,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,mBAAmB;gBACrB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 9286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/financial-dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { \n  IconTrendingUp, \n  IconTrendingDown,\n  IconCash,\n  IconCreditCard,\n  IconDeviceMobile,\n  IconBuildingBank,\n  IconAlertTriangle,\n  IconRefresh,\n  IconCalendar,\n  IconChartBar\n} from '@tabler/icons-react';\nimport { reportsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { toast } from 'sonner';\n\ninterface FinancialMetrics {\n  dailyRevenue: {\n    date: string;\n    totalRevenue: number;\n    paymentCount: number;\n    paymentMethods: Record<string, { amount: number; count: number }>;\n  } | null;\n  monthlyRevenue: {\n    year: number;\n    month: number;\n    totalRevenue: number;\n    dailyBreakdown: Array<{ date: string; revenue: number }>;\n  } | null;\n  outstandingBalances: {\n    totalOutstanding: number;\n    overdueAmount: number;\n    billsCount: number;\n    overdueBillsCount: number;\n    bills: Array<{\n      id: string;\n      billNumber: string;\n      patient: string;\n      amount: number;\n      dueDate: string;\n      daysOverdue: number;\n    }>;\n  } | null;\n}\n\nexport function FinancialDashboard() {\n  const { hasPermission } = useRole();\n  const [metrics, setMetrics] = useState<FinancialMetrics>({\n    dailyRevenue: null,\n    monthlyRevenue: null,\n    outstandingBalances: null,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n  const [selectedMonth, setSelectedMonth] = useState({\n    year: new Date().getFullYear(),\n    month: new Date().getMonth() + 1,\n  });\n  const [refreshing, setRefreshing] = useState(false);\n\n  // Check permissions\n  if (!hasPermission('canViewDetailedFinancials')) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertTriangle className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">权限不足</h3>\n          <p className=\"text-muted-foreground\">\n            您没有权限查看详细的财务报表\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  const fetchMetrics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const [dailyRevenue, monthlyRevenue, outstandingBalances] = await Promise.all([\n        reportsAPI.getDailyRevenue(selectedDate).catch(() => null),\n        reportsAPI.getMonthlyRevenue(selectedMonth.year, selectedMonth.month).catch(() => null),\n        reportsAPI.getOutstandingBalances().catch(() => null),\n      ]);\n\n      setMetrics({\n        dailyRevenue,\n        monthlyRevenue,\n        outstandingBalances,\n      });\n    } catch (err) {\n      console.error('Failed to fetch financial metrics:', err);\n      const errorMessage = err instanceof BillingAPIError \n        ? err.message \n        : '加载财务数据失败，请稍后重试';\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchMetrics();\n  }, [selectedDate, selectedMonth]);\n\n  const handleRefresh = () => {\n    setRefreshing(true);\n    fetchMetrics();\n  };\n\n  const getPaymentMethodIcon = (method: string) => {\n    switch (method) {\n      case 'cash':\n        return IconCash;\n      case 'card':\n        return IconCreditCard;\n      case 'wechat':\n      case 'alipay':\n        return IconDeviceMobile;\n      case 'transfer':\n        return IconBuildingBank;\n      default:\n        return IconCash;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载财务数据中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold tracking-tight flex items-center gap-2\">\n            <IconChartBar className=\"size-6\" />\n            财务报表\n          </h2>\n          <p className=\"text-muted-foreground\">\n            查看收入统计、支付分析和应收账款\n          </p>\n        </div>\n        <Button \n          variant=\"outline\" \n          size=\"sm\" \n          onClick={handleRefresh}\n          disabled={refreshing}\n        >\n          <IconRefresh className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />\n          刷新数据\n        </Button>\n      </div>\n\n      {/* Date Controls */}\n      <div className=\"flex items-center gap-4\">\n        <div className=\"flex items-center gap-2\">\n          <IconCalendar className=\"h-4 w-4\" />\n          <span className=\"text-sm font-medium\">日期选择:</span>\n          <input\n            type=\"date\"\n            value={selectedDate}\n            onChange={(e) => setSelectedDate(e.target.value)}\n            className=\"px-3 py-1 border rounded-md text-sm\"\n          />\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-sm font-medium\">月份选择:</span>\n          <Select \n            value={`${selectedMonth.year}-${selectedMonth.month}`}\n            onValueChange={(value) => {\n              const [year, month] = value.split('-').map(Number);\n              setSelectedMonth({ year, month });\n            }}\n          >\n            <SelectTrigger className=\"w-40\">\n              <SelectValue />\n            </SelectTrigger>\n            <SelectContent>\n              {Array.from({ length: 12 }, (_, i) => {\n                const month = i + 1;\n                const year = new Date().getFullYear();\n                return (\n                  <SelectItem key={`${year}-${month}`} value={`${year}-${month}`}>\n                    {year}年{month}月\n                  </SelectItem>\n                );\n              })}\n            </SelectContent>\n          </Select>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <div className=\"flex items-center\">\n            <IconAlertTriangle className=\"h-4 w-4 text-red-500 mr-2\" />\n            <span className=\"text-red-700 text-sm\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Daily Revenue Card */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <IconTrendingUp className=\"h-5 w-5\" />\n            日收入统计\n          </CardTitle>\n          <CardDescription>\n            {new Date(selectedDate).toLocaleDateString('zh-CN')} 的收入详情\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {metrics.dailyRevenue ? (\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {billingUtils.formatCurrency(metrics.dailyRevenue.totalRevenue)}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">总收入</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold\">\n                    {metrics.dailyRevenue.paymentCount}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">支付笔数</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold\">\n                    {metrics.dailyRevenue.paymentCount > 0 \n                      ? billingUtils.formatCurrency(metrics.dailyRevenue.totalRevenue / metrics.dailyRevenue.paymentCount)\n                      : billingUtils.formatCurrency(0)\n                    }\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">平均金额</div>\n                </div>\n              </div>\n\n              <Separator />\n\n              <div>\n                <h4 className=\"font-medium mb-3\">支付方式分布</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                  {Object.entries(metrics.dailyRevenue.paymentMethods).map(([method, data]) => {\n                    const Icon = getPaymentMethodIcon(method);\n                    return (\n                      <div key={method} className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\">\n                        <div className=\"flex items-center gap-2\">\n                          <Icon className=\"h-4 w-4\" />\n                          <span className=\"text-sm font-medium\">\n                            {billingUtils.getPaymentMethodName(method)}\n                          </span>\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-sm font-semibold\">\n                            {billingUtils.formatCurrency(data.amount)}\n                          </div>\n                          <div className=\"text-xs text-muted-foreground\">\n                            {data.count} 笔\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              暂无当日收入数据\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Monthly Revenue Card */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <IconChartBar className=\"h-5 w-5\" />\n            月度收入统计\n          </CardTitle>\n          <CardDescription>\n            {selectedMonth.year}年{selectedMonth.month}月的收入趋势\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {metrics.monthlyRevenue ? (\n            <div className=\"space-y-4\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600\">\n                  {billingUtils.formatCurrency(metrics.monthlyRevenue.totalRevenue)}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">月度总收入</div>\n              </div>\n\n              <Separator />\n\n              <div>\n                <h4 className=\"font-medium mb-3\">每日收入明细</h4>\n                <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n                  {metrics.monthlyRevenue.dailyBreakdown.map((day) => (\n                    <div key={day.date} className=\"flex justify-between items-center p-2 hover:bg-muted/50 rounded\">\n                      <span className=\"text-sm\">\n                        {new Date(day.date).toLocaleDateString('zh-CN')}\n                      </span>\n                      <span className=\"font-medium\">\n                        {billingUtils.formatCurrency(day.revenue)}\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              暂无月度收入数据\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Outstanding Balances Card */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <IconAlertTriangle className=\"h-5 w-5\" />\n            应收账款\n          </CardTitle>\n          <CardDescription>\n            待收款项和逾期账单统计\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {metrics.outstandingBalances ? (\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-600\">\n                    {billingUtils.formatCurrency(metrics.outstandingBalances.totalOutstanding)}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">总待收金额</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {billingUtils.formatCurrency(metrics.outstandingBalances.overdueAmount)}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">逾期金额</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold\">\n                    {metrics.outstandingBalances.billsCount}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">待收账单</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {metrics.outstandingBalances.overdueBillsCount}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">逾期账单</div>\n                </div>\n              </div>\n\n              {metrics.outstandingBalances.bills.length > 0 && (\n                <>\n                  <Separator />\n                  <div>\n                    <h4 className=\"font-medium mb-3\">逾期账单详情</h4>\n                    <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n                      {metrics.outstandingBalances.bills\n                        .filter(bill => bill.daysOverdue > 0)\n                        .map((bill) => (\n                        <div key={bill.id} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                          <div>\n                            <div className=\"font-medium text-sm\">{bill.billNumber}</div>\n                            <div className=\"text-xs text-muted-foreground\">{bill.patient}</div>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"font-semibold text-red-600\">\n                              {billingUtils.formatCurrency(bill.amount)}\n                            </div>\n                            <Badge variant=\"destructive\" className=\"text-xs\">\n                              逾期 {bill.daysOverdue} 天\n                            </Badge>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </>\n              )}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              暂无应收账款数据\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AAtBA;;;;;;;;;;;;AAqDO,SAAS;IACd,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAoB;QACvD,cAAc;QACd,gBAAgB;QAChB,qBAAqB;IACvB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACvF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,MAAM,IAAI,OAAO,WAAW;QAC5B,OAAO,IAAI,OAAO,QAAQ,KAAK;IACjC;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,oBAAoB;IACpB,IAAI,CAAC,cAAc,8BAA8B;QAC/C,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,sUAAA,CAAA,oBAAiB;wBAAC,WAAU;;;;;;kCAC7B,6VAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,CAAC,cAAc,gBAAgB,oBAAoB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC5E,4HAAA,CAAA,aAAU,CAAC,eAAe,CAAC,cAAc,KAAK,CAAC,IAAM;gBACrD,4HAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,cAAc,IAAI,EAAE,cAAc,KAAK,EAAE,KAAK,CAAC,IAAM;gBAClF,4HAAA,CAAA,aAAU,CAAC,sBAAsB,GAAG,KAAK,CAAC,IAAM;aACjD;YAED,WAAW;gBACT;gBACA;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM,eAAe,eAAe,4HAAA,CAAA,kBAAe,GAC/C,IAAI,OAAO,GACX;YACJ,SAAS;YACT,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;YACX,cAAc;QAChB;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,gBAAgB;QACpB,cAAc;QACd;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO,oTAAA,CAAA,WAAQ;YACjB,KAAK;gBACH,OAAO,gUAAA,CAAA,iBAAc;YACvB,KAAK;YACL,KAAK;gBACH,OAAO,oUAAA,CAAA,mBAAgB;YACzB,KAAK;gBACH,OAAO,oUAAA,CAAA,mBAAgB;YACzB;gBACE,OAAO,oTAAA,CAAA,WAAQ;QACnB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;;kDACZ,6VAAC,4TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAW;;;;;;;0CAGrC,6VAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;;0CAEV,6VAAC,0TAAA,CAAA,cAAW;gCAAC,WAAW,CAAC,aAAa,EAAE,aAAa,iBAAiB,IAAI;;;;;;4BAAI;;;;;;;;;;;;;0BAMlF,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,4TAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6VAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,6VAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;;;;;;;;;;;kCAGd,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,6VAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,GAAG,cAAc,IAAI,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE;gCACrD,eAAe,CAAC;oCACd,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC;oCAC3C,iBAAiB;wCAAE;wCAAM;oCAAM;gCACjC;;kDAEA,6VAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6VAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6VAAC,kIAAA,CAAA,gBAAa;kDACX,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAG,GAAG,CAAC,GAAG;4CAC9B,MAAM,QAAQ,IAAI;4CAClB,MAAM,OAAO,IAAI,OAAO,WAAW;4CACnC,qBACE,6VAAC,kIAAA,CAAA,aAAU;gDAA0B,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO;;oDAC3D;oDAAK;oDAAE;oDAAM;;+CADC,GAAG,KAAK,CAAC,EAAE,OAAO;;;;;wCAIvC;;;;;;;;;;;;;;;;;;;;;;;;YAOP,uBACC,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,sUAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;sCAC7B,6VAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAM9C,6VAAC,gIAAA,CAAA,OAAI;;kCACH,6VAAC,gIAAA,CAAA,aAAU;;0CACT,6VAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6VAAC,gUAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGxC,6VAAC,gIAAA,CAAA,kBAAe;;oCACb,IAAI,KAAK,cAAc,kBAAkB,CAAC;oCAAS;;;;;;;;;;;;;kCAGxD,6VAAC,gIAAA,CAAA,cAAW;kCACT,QAAQ,YAAY,iBACnB,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;8DACZ,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,YAAY,CAAC,YAAY;;;;;;8DAEhE,6VAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;8DACZ,QAAQ,YAAY,CAAC,YAAY;;;;;;8DAEpC,6VAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;8DACZ,QAAQ,YAAY,CAAC,YAAY,GAAG,IACjC,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,YAAY,CAAC,YAAY,GAAG,QAAQ,YAAY,CAAC,YAAY,IACjG,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC;;;;;;8DAGlC,6VAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAInD,6VAAC,qIAAA,CAAA,YAAS;;;;;8CAEV,6VAAC;;sDACC,6VAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,6VAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,QAAQ,YAAY,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,KAAK;gDACtE,MAAM,OAAO,qBAAqB;gDAClC,qBACE,6VAAC;oDAAiB,WAAU;;sEAC1B,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAK,WAAU;;;;;;8EAChB,6VAAC;oEAAK,WAAU;8EACb,4HAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC;;;;;;;;;;;;sEAGvC,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAI,WAAU;8EACZ,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,MAAM;;;;;;8EAE1C,6VAAC;oEAAI,WAAU;;wEACZ,KAAK,KAAK;wEAAC;;;;;;;;;;;;;;mDAZR;;;;;4CAiBd;;;;;;;;;;;;;;;;;iDAKN,6VAAC;4BAAI,WAAU;sCAAyC;;;;;;;;;;;;;;;;;0BAQ9D,6VAAC,gIAAA,CAAA,OAAI;;kCACH,6VAAC,gIAAA,CAAA,aAAU;;0CACT,6VAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6VAAC,4TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGtC,6VAAC,gIAAA,CAAA,kBAAe;;oCACb,cAAc,IAAI;oCAAC;oCAAE,cAAc,KAAK;oCAAC;;;;;;;;;;;;;kCAG9C,6VAAC,gIAAA,CAAA,cAAW;kCACT,QAAQ,cAAc,iBACrB,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACZ,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,cAAc,CAAC,YAAY;;;;;;sDAElE,6VAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,6VAAC,qIAAA,CAAA,YAAS;;;;;8CAEV,6VAAC;;sDACC,6VAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,6VAAC;4CAAI,WAAU;sDACZ,QAAQ,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,oBAC1C,6VAAC;oDAAmB,WAAU;;sEAC5B,6VAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,IAAI,IAAI,EAAE,kBAAkB,CAAC;;;;;;sEAEzC,6VAAC;4DAAK,WAAU;sEACb,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,OAAO;;;;;;;mDALlC,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;iDAa1B,6VAAC;4BAAI,WAAU;sCAAyC;;;;;;;;;;;;;;;;;0BAQ9D,6VAAC,gIAAA,CAAA,OAAI;;kCACH,6VAAC,gIAAA,CAAA,aAAU;;0CACT,6VAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6VAAC,sUAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG3C,6VAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6VAAC,gIAAA,CAAA,cAAW;kCACT,QAAQ,mBAAmB,iBAC1B,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;8DACZ,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,mBAAmB,CAAC,gBAAgB;;;;;;8DAE3E,6VAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;8DACZ,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,QAAQ,mBAAmB,CAAC,aAAa;;;;;;8DAExE,6VAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;8DACZ,QAAQ,mBAAmB,CAAC,UAAU;;;;;;8DAEzC,6VAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;8DACZ,QAAQ,mBAAmB,CAAC,iBAAiB;;;;;;8DAEhD,6VAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;gCAIlD,QAAQ,mBAAmB,CAAC,KAAK,CAAC,MAAM,GAAG,mBAC1C;;sDACE,6VAAC,qIAAA,CAAA,YAAS;;;;;sDACV,6VAAC;;8DACC,6VAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,6VAAC;oDAAI,WAAU;8DACZ,QAAQ,mBAAmB,CAAC,KAAK,CAC/B,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,GAAG,GAClC,GAAG,CAAC,CAAC,qBACN,6VAAC;4DAAkB,WAAU;;8EAC3B,6VAAC;;sFACC,6VAAC;4EAAI,WAAU;sFAAuB,KAAK,UAAU;;;;;;sFACrD,6VAAC;4EAAI,WAAU;sFAAiC,KAAK,OAAO;;;;;;;;;;;;8EAE9D,6VAAC;oEAAI,WAAU;;sFACb,6VAAC;4EAAI,WAAU;sFACZ,4HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,KAAK,MAAM;;;;;;sFAE1C,6VAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAc,WAAU;;gFAAU;gFAC3C,KAAK,WAAW;gFAAC;;;;;;;;;;;;;;2DAVjB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;iDAqB7B,6VAAC;4BAAI,WAAU;sCAAyC;;;;;;;;;;;;;;;;;;;;;;;AAQpE", "debugId": null}}, {"offset": {"line": 10333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/billing/billing-tabs.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';\nimport { BillingList } from './billing-list';\nimport { AppointmentToBill } from './appointment-to-bill';\nimport { ReceiptManager } from './receipt-manager';\nimport { FinancialDashboard } from './financial-dashboard';\nimport { \n  IconReceipt, \n  IconCalendarEvent, \n  IconFileText,\n  IconChartBar\n} from '@tabler/icons-react';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { Bill } from '@/types/clinic';\nimport { toast } from 'sonner';\n\ninterface BillingTabsProps {\n  defaultTab?: string;\n  className?: string;\n}\n\nexport function BillingTabs({ defaultTab = 'bills', className }: BillingTabsProps) {\n  const { hasPermission } = useRole();\n  const [activeTab, setActiveTab] = useState(defaultTab);\n\n  const handleBillGenerated = (bill: Bill) => {\n    toast.success(`账单生成成功！账单编号: ${bill.billNumber}`);\n    // Switch to bills tab to show the newly created bill\n    setActiveTab('bills');\n  };\n\n  return (\n    <div className={className}>\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-2 lg:grid-cols-4\">\n          <TabsTrigger value=\"bills\" className=\"flex items-center gap-2\">\n            <IconReceipt className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline\">账单管理</span>\n            <span className=\"sm:hidden\">账单</span>\n          </TabsTrigger>\n          \n          <PermissionGate permission=\"canCreateBills\">\n            <TabsTrigger value=\"generate\" className=\"flex items-center gap-2\">\n              <IconCalendarEvent className=\"h-4 w-4\" />\n              <span className=\"hidden sm:inline\">预约生成</span>\n              <span className=\"sm:hidden\">生成</span>\n            </TabsTrigger>\n          </PermissionGate>\n          \n          <PermissionGate permission=\"canGenerateReceipts\">\n            <TabsTrigger value=\"receipts\" className=\"flex items-center gap-2\">\n              <IconFileText className=\"h-4 w-4\" />\n              <span className=\"hidden sm:inline\">收据管理</span>\n              <span className=\"sm:hidden\">收据</span>\n            </TabsTrigger>\n          </PermissionGate>\n          \n          <PermissionGate permission=\"canViewDetailedFinancials\">\n            <TabsTrigger value=\"reports\" className=\"flex items-center gap-2\">\n              <IconChartBar className=\"h-4 w-4\" />\n              <span className=\"hidden sm:inline\">财务报表</span>\n              <span className=\"sm:hidden\">报表</span>\n            </TabsTrigger>\n          </PermissionGate>\n        </TabsList>\n\n        {/* Bills Management Tab */}\n        <TabsContent value=\"bills\" className=\"space-y-6\">\n          <div>\n            <h2 className=\"text-2xl font-bold tracking-tight mb-2\">账单管理</h2>\n            <p className=\"text-muted-foreground mb-6\">\n              管理所有账单，处理支付和查看账单状态\n            </p>\n          </div>\n          <BillingList />\n        </TabsContent>\n\n        {/* Generate from Appointments Tab */}\n        <PermissionGate permission=\"canCreateBills\">\n          <TabsContent value=\"generate\" className=\"space-y-6\">\n            <AppointmentToBill onBillGenerated={handleBillGenerated} />\n          </TabsContent>\n        </PermissionGate>\n\n        {/* Receipt Management Tab */}\n        <PermissionGate permission=\"canGenerateReceipts\">\n          <TabsContent value=\"receipts\" className=\"space-y-6\">\n            <ReceiptManager />\n          </TabsContent>\n        </PermissionGate>\n\n        {/* Financial Reports Tab */}\n        <PermissionGate permission=\"canViewDetailedFinancials\">\n          <TabsContent value=\"reports\" className=\"space-y-6\">\n            <FinancialDashboard />\n          </TabsContent>\n        </PermissionGate>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AAEA;AAhBA;;;;;;;;;;;AAuBO,SAAS,YAAY,EAAE,aAAa,OAAO,EAAE,SAAS,EAAoB;IAC/E,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,sBAAsB,CAAC;QAC3B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,KAAK,UAAU,EAAE;QAC/C,qDAAqD;QACrD,aAAa;IACf;IAEA,qBACE,6VAAC;QAAI,WAAW;kBACd,cAAA,6VAAC,gIAAA,CAAA,OAAI;YAAC,OAAO;YAAW,eAAe;YAAc,WAAU;;8BAC7D,6VAAC,gIAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,6VAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;;8CACnC,6VAAC,0TAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6VAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,6VAAC;oCAAK,WAAU;8CAAY;;;;;;;;;;;;sCAG9B,6VAAC,8HAAA,CAAA,iBAAc;4BAAC,YAAW;sCACzB,cAAA,6VAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,6VAAC,sUAAA,CAAA,oBAAiB;wCAAC,WAAU;;;;;;kDAC7B,6VAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6VAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;sCAIhC,6VAAC,8HAAA,CAAA,iBAAc;4BAAC,YAAW;sCACzB,cAAA,6VAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,6VAAC,4TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6VAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6VAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;sCAIhC,6VAAC,8HAAA,CAAA,iBAAc;4BAAC,YAAW;sCACzB,cAAA,6VAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;;kDACrC,6VAAC,4TAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6VAAC;wCAAK,WAAU;kDAAmB;;;;;;kDACnC,6VAAC;wCAAK,WAAU;kDAAY;;;;;;;;;;;;;;;;;;;;;;;8BAMlC,6VAAC,gIAAA,CAAA,cAAW;oBAAC,OAAM;oBAAQ,WAAU;;sCACnC,6VAAC;;8CACC,6VAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6VAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6VAAC,gJAAA,CAAA,cAAW;;;;;;;;;;;8BAId,6VAAC,8HAAA,CAAA,iBAAc;oBAAC,YAAW;8BACzB,cAAA,6VAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,6VAAC,0JAAA,CAAA,oBAAiB;4BAAC,iBAAiB;;;;;;;;;;;;;;;;8BAKxC,6VAAC,8HAAA,CAAA,iBAAc;oBAAC,YAAW;8BACzB,cAAA,6VAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;kCACtC,cAAA,6VAAC,mJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;8BAKnB,6VAAC,8HAAA,CAAA,iBAAc;oBAAC,YAAW;8BACzB,cAAA,6VAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,6VAAC,uJAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/B", "debugId": null}}]}